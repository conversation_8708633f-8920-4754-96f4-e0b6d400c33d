package handler

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"

	"kubeops/internal/model"
	"kubeops/internal/response"
	"kubeops/internal/service"
)

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// UserStatusRequest 用户状态请求
type UserStatusRequest struct {
	Status model.UserStatus `json:"status" binding:"required"`
}

// UserHandler 处理用户相关API请求
type UserHandler struct {
	userService      service.UserService
	casbinService    *service.CasbinService
	userGroupService service.UserGroupService
	tracer           trace.Tracer
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService, casbinService *service.CasbinService, userGroupService service.UserGroupService, tracer trace.Tracer) *UserHandler {
	return &UserHandler{
		userService:      userService,
		casbinService:    casbinService,
		userGroupService: userGroupService,
		tracer:           tracer,
	}
}

// GetUser 获取用户详情
func (h *UserHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, user)
}

// ListUsers 获取用户列表
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))

	users, total, err := h.userService.ListUsers(c.Request.Context(), page, size)
	if err != nil {
		response.ServerError(c, err)
		return
	}
	response.Success(c, gin.H{
		"users": users,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetUserInfo 获取当前登录用户的信息
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	fmt.Printf("GetUserInfo: userID=%v, exists=%v, type=%T\n", userID, exists, userID)

	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}

	// 转换为 int64 类型
	var userIDInt64 int64
	switch id := userID.(type) {
	case uint:
		userIDInt64 = int64(id)
		fmt.Printf("GetUserInfo: userID is uint=%d\n", id)
	case int64:
		userIDInt64 = id
		fmt.Printf("GetUserInfo: userID is int64=%d\n", id)
	case float64:
		userIDInt64 = int64(id)
		fmt.Printf("GetUserInfo: userID is float64=%f\n", id)
	default:
		fmt.Printf("GetUserInfo: userID has unknown type\n")
		response.ServerError(c, nil)
		return
	}

	fmt.Printf("GetUserInfo: converted userIDInt64=%d\n", userIDInt64)

	// 获取用户详情
	user, err := h.userService.GetUserByID(c.Request.Context(), uint(userIDInt64))
	if err != nil {
		fmt.Printf("GetUserInfo: error getting user: %v\n", err)
		response.ServerError(c, err)
		return
	}

	fmt.Printf("GetUserInfo: got user=%v\n", user)

	// 获取用户组（替代角色）
	groups, err := h.userGroupService.GetUserGroups(c.Request.Context(), uint(userIDInt64))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	// 前端期望的响应格式
	userData := map[string]interface{}{
		"id":       user.ID,
		"username": user.Username,
		"email":    user.Email,
		"avatar":   user.Avatar,
		"name":     user.Nickname,
	}

	// 返回用户信息和用户组
	response.Success(c, gin.H{
		"groups":       groups, // 用户组列表
		"introduction": userData["email"],
		"avatar":       userData["avatar"],
		"name":         userData["username"],
	})
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req model.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的用户数据")
		return
	}

	user, err := h.userService.CreateUser(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, user, response.WithMessage("用户创建成功"))
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req model.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的用户数据")
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), uint(userID), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, user, response.WithMessage("用户更新成功"))
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	if err := h.userService.DeleteUser(c.Request.Context(), uint(userID)); err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("用户删除成功"))
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	err = h.userService.ChangePassword(c.Request.Context(), uint(userID), req.OldPassword, req.NewPassword)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("密码修改成功"))
}

// ActivateUser 激活用户
func (h *UserHandler) ActivateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.ActivateUser(c.Request.Context(), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("用户激活成功"))
}

// DeactivateUser 停用用户
func (h *UserHandler) DeactivateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.DeactivateUser(c.Request.Context(), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("用户停用成功"))
}

// LockUser 锁定用户
func (h *UserHandler) LockUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.LockUser(c.Request.Context(), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("用户锁定成功"))
}

// UnlockUser 解锁用户
func (h *UserHandler) UnlockUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.UnlockUser(c.Request.Context(), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("用户解锁成功"))
}

package handler

import (
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/auth"
	"kubeops/internal/logger"
	"kubeops/internal/middleware"
	"kubeops/internal/response"
	"kubeops/internal/service"
)

// AuthHandler 处理认证相关请求
type AuthHandler struct {
	authService service.AuthService
	tracer      trace.Tracer
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService service.AuthService, tracer trace.Tracer) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		tracer:      tracer,
	}
}

// LoginRequest 登录请求 - 重构版本
type LoginRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required"`
	RememberMe bool   `json:"remember_me"`

	// 设备信息（可选，前端可以提供）
	DeviceID   string `json:"device_id,omitempty"`
	DeviceType string `json:"device_type,omitempty"` // web, mobile, desktop
	Platform   string `json:"platform,omitempty"`    // Windows, macOS, Linux, iOS, Android
	Browser    string `json:"browser,omitempty"`     // Chrome, Firefox, Safari, Edge
}

// RefreshTokenRequest 刷新Token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	LogoutAll bool `json:"logout_all"` // 是否登出所有设备
}

// Login 用户登录 - 重构版本
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	logger.InfoContext(c.Request.Context(), "收到登录请求")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorContext(c.Request.Context(), "请求数据绑定失败", zap.Error(err))
		response.BadRequest(c, "无效的请求数据")
		return
	}

	logger.InfoContext(c.Request.Context(), "尝试登录", zap.String("username", req.Username))

	// 构建设备信息
	deviceInfo := h.buildDeviceInfo(c, &req)

	// 调用新的登录方法
	var tokenPairResponse *service.TokenPairResponse
	var err error

	if req.RememberMe {
		tokenPairResponse, err = h.authService.LoginWithRememberMe(c.Request.Context(), req.Username, req.Password, deviceInfo, true)
	} else {
		tokenPairResponse, err = h.authService.Login(c.Request.Context(), req.Username, req.Password, deviceInfo)
	}

	if err != nil {
		logger.WarnContext(c.Request.Context(), "登录失败",
			zap.String("username", req.Username),
			zap.String("ip", deviceInfo.IPAddress),
			zap.Error(err))
		response.BadRequest(c, "用户名或密码错误")
		return
	}

	logger.InfoContext(c.Request.Context(), "登录成功",
		zap.String("username", req.Username),
		zap.String("session_id", tokenPairResponse.SessionID),
		zap.String("device_type", deviceInfo.DeviceType))

	// 设置Cookie（Access Token）
	accessTokenExpiry := int(tokenPairResponse.ExpiresIn)
	c.SetCookie(
		"access_token",
		tokenPairResponse.AccessToken,
		accessTokenExpiry,
		"/",
		"",
		false, // secure - 在生产环境中应该设置为true
		true,  // httpOnly
	)

	// 设置Refresh Token Cookie（更长的过期时间）
	refreshTokenExpiry := 7 * 24 * 3600 // 7天
	c.SetCookie(
		"refresh_token",
		tokenPairResponse.RefreshToken,
		refreshTokenExpiry,
		"/",
		"",
		false, // secure
		true,  // httpOnly
	)

	// 兼容性：也设置旧的Cookie名称
	c.SetCookie(
		middleware.TokenCookieName,
		tokenPairResponse.AccessToken,
		accessTokenExpiry,
		"/",
		"",
		false,
		true,
	)

	response.Success(c, tokenPairResponse)
}

// RefreshToken 刷新Token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	logger.InfoContext(c.Request.Context(), "收到Token刷新请求")

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorContext(c.Request.Context(), "请求数据绑定失败", zap.Error(err))
		response.BadRequest(c, "无效的请求数据")
		return
	}

	// 调用刷新Token服务
	tokenPairResponse, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		logger.WarnContext(c.Request.Context(), "Token刷新失败", zap.Error(err))
		response.Unauthorized(c, "Token刷新失败")
		return
	}

	logger.InfoContext(c.Request.Context(), "Token刷新成功",
		zap.String("username", tokenPairResponse.User.Username),
		zap.String("session_id", tokenPairResponse.SessionID))

	// 更新Cookie
	accessTokenExpiry := int(tokenPairResponse.ExpiresIn)
	c.SetCookie(
		"access_token",
		tokenPairResponse.AccessToken,
		accessTokenExpiry,
		"/",
		"",
		false,
		true,
	)

	refreshTokenExpiry := 7 * 24 * 3600 // 7天
	c.SetCookie(
		"refresh_token",
		tokenPairResponse.RefreshToken,
		refreshTokenExpiry,
		"/",
		"",
		false,
		true,
	)

	// 兼容性Cookie
	c.SetCookie(
		middleware.TokenCookieName,
		tokenPairResponse.AccessToken,
		accessTokenExpiry,
		"/",
		"",
		false,
		true,
	)

	response.Success(c, tokenPairResponse)
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	var req LogoutRequest
	logger.InfoContext(c.Request.Context(), "收到登出请求")

	// 绑定请求数据（可选）
	c.ShouldBindJSON(&req)

	// 从请求中提取Token
	token := h.extractTokenFromRequest(c)
	if token == "" {
		logger.WarnContext(c.Request.Context(), "登出请求中没有找到Token")
		response.BadRequest(c, "未找到有效的Token")
		return
	}

	// 调用登出服务
	err := h.authService.Logout(c.Request.Context(), token, req.LogoutAll)
	if err != nil {
		logger.ErrorContext(c.Request.Context(), "登出失败", zap.Error(err))
		response.BadRequest(c, "登出失败")
		return
	}

	// 清除Cookie
	h.clearAuthCookies(c)

	logger.InfoContext(c.Request.Context(), "登出成功")
	response.Success(c, gin.H{"message": "登出成功"})
}

// GetUserSessions 获取用户会话列表
func (h *AuthHandler) GetUserSessions(c *gin.Context) {
	// 从JWT中获取用户ID
	userIDValue, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		response.Unauthorized(c, "用户ID格式错误")
		return
	}

	sessions, err := h.authService.GetUserSessions(c.Request.Context(), userID)
	if err != nil {
		logger.ErrorContext(c.Request.Context(), "获取用户会话失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		response.BadRequest(c, "获取会话列表失败")
		return
	}

	response.Success(c, gin.H{
		"sessions":    sessions,
		"total_count": len(sessions),
	})
}

// RevokeSession 撤销指定会话
func (h *AuthHandler) RevokeSession(c *gin.Context) {
	// 从JWT中获取用户ID
	userIDValue, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}
	userID, ok := userIDValue.(uint)
	if !ok {
		response.Unauthorized(c, "用户ID格式错误")
		return
	}

	// 从URL参数获取会话ID
	sessionID := c.Param("sessionId")
	if sessionID == "" {
		response.BadRequest(c, "会话ID不能为空")
		return
	}

	err := h.authService.RevokeSession(c.Request.Context(), userID, sessionID)
	if err != nil {
		logger.ErrorContext(c.Request.Context(), "撤销会话失败",
			zap.Uint("user_id", userID),
			zap.String("session_id", sessionID),
			zap.Error(err))
		response.BadRequest(c, "撤销会话失败")
		return
	}

	logger.InfoContext(c.Request.Context(), "会话撤销成功",
		zap.Uint("user_id", userID),
		zap.String("session_id", sessionID))

	response.Success(c, gin.H{"message": "会话撤销成功"})
}

// GetCurrentUser 获取当前用户信息
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	// 从JWT中获取用户信息
	userClaimsValue, exists := c.Get("user_claims")
	if !exists {
		response.Unauthorized(c, "未找到用户信息")
		return
	}
	jwtClaims, ok := userClaimsValue.(*auth.JWTClaims)
	if !ok {
		response.Unauthorized(c, "用户信息格式错误")
		return
	}

	// 直接使用JWT中的用户信息
	// 在实际应用中，可能需要从数据库获取最新的用户信息

	// 构建响应数据，直接使用JWT中的信息
	userInfo := &service.UserInfo{
		UserID:      jwtClaims.UserID,
		Username:    jwtClaims.Username,
		Email:       jwtClaims.Email,
		DisplayName: jwtClaims.DisplayName,
		Groups:      jwtClaims.Groups,
		Permissions: jwtClaims.Permissions,
		Status:      "active", // 从JWT中无法获取状态，默认为active
	}

	response.Success(c, userInfo)
}

// TokenData 令牌数据
type TokenData struct {
	Token string `json:"token" binding:"required"`
}

// ValidateToken 验证令牌
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	var data TokenData
	if err := c.ShouldBindJSON(&data); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	claims, err := h.authService.VerifyToken(c.Request.Context(), data.Token)
	if err != nil {
		response.Unauthorized(c, "无效的令牌")
		return
	}

	response.Success(c, gin.H{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"groups":   claims.Groups, // 使用用户组而不是角色
	})
}

// 旧版RefreshToken方法已删除，使用新版本

// 旧版Logout方法已删除，使用新版本

// 新增的辅助方法

// buildDeviceInfo 构建设备信息
func (h *AuthHandler) buildDeviceInfo(c *gin.Context, req *LoginRequest) *service.DeviceInfo {
	// 获取客户端IP
	clientIP := h.getClientIP(c)

	// 获取User-Agent
	userAgent := c.GetHeader("User-Agent")

	// 构建设备信息
	deviceInfo := &service.DeviceInfo{
		IPAddress: clientIP,
		UserAgent: userAgent,
	}

	// 如果请求中包含设备信息，使用请求中的信息
	if req.DeviceID != "" {
		deviceInfo.DeviceID = req.DeviceID
	} else {
		// 生成设备ID（可以基于IP和User-Agent的哈希）
		deviceInfo.DeviceID = h.generateDeviceID(clientIP, userAgent)
	}

	if req.DeviceType != "" {
		deviceInfo.DeviceType = req.DeviceType
	} else {
		// 根据User-Agent推断设备类型
		deviceInfo.DeviceType = h.inferDeviceType(userAgent)
	}

	if req.Platform != "" {
		deviceInfo.Platform = req.Platform
	}

	if req.Browser != "" {
		deviceInfo.Browser = req.Browser
	}

	return deviceInfo
}

// getClientIP 获取客户端真实IP
func (h *AuthHandler) getClientIP(c *gin.Context) string {
	// 尝试从各种头部获取真实IP
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		if idx := strings.Index(clientIP, ","); idx != -1 {
			clientIP = clientIP[:idx]
		}
		clientIP = strings.TrimSpace(clientIP)
		if clientIP != "" && clientIP != "unknown" {
			return clientIP
		}
	}

	clientIP = c.GetHeader("X-Real-IP")
	if clientIP != "" && clientIP != "unknown" {
		return clientIP
	}

	clientIP = c.GetHeader("X-Original-Forwarded-For")
	if clientIP != "" && clientIP != "unknown" {
		return clientIP
	}

	// 最后使用RemoteAddr
	if ip, _, err := net.SplitHostPort(c.Request.RemoteAddr); err == nil {
		return ip
	}

	return c.ClientIP()
}

// generateDeviceID 生成设备ID
func (h *AuthHandler) generateDeviceID(ip, userAgent string) string {
	// 简单的设备ID生成策略，实际应用中可以使用更复杂的算法
	return fmt.Sprintf("device_%x", time.Now().UnixNano())
}

// inferDeviceType 推断设备类型
func (h *AuthHandler) inferDeviceType(userAgent string) string {
	userAgent = strings.ToLower(userAgent)

	if strings.Contains(userAgent, "mobile") ||
		strings.Contains(userAgent, "android") ||
		strings.Contains(userAgent, "iphone") {
		return "mobile"
	}

	if strings.Contains(userAgent, "tablet") ||
		strings.Contains(userAgent, "ipad") {
		return "tablet"
	}

	return "web"
}

// extractTokenFromRequest 从请求中提取Token
func (h *AuthHandler) extractTokenFromRequest(c *gin.Context) string {
	// 1. 从Authorization header提取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		return authHeader
	}

	// 2. 从Cookie提取
	if token, err := c.Cookie("access_token"); err == nil && token != "" {
		return token
	}

	// 3. 兼容性：从旧Cookie名称提取
	if token, err := c.Cookie(middleware.TokenCookieName); err == nil && token != "" {
		return token
	}

	return ""
}

// clearAuthCookies 清除认证相关的Cookie
func (h *AuthHandler) clearAuthCookies(c *gin.Context) {
	// 清除所有认证相关的Cookie
	cookieNames := []string{
		"access_token",
		"refresh_token",
		middleware.TokenCookieName,
	}

	for _, cookieName := range cookieNames {
		c.SetCookie(
			cookieName,
			"",
			-1, // 过期时间设为过去
			"/",
			"",
			false,
			true,
		)
	}
}

// convertUserStatus 转换用户状态
func (h *AuthHandler) convertUserStatus(status int) string {
	switch status {
	case 1:
		return "active"
	case 0:
		return "inactive"
	case -1:
		return "banned"
	default:
		return "unknown"
	}
}

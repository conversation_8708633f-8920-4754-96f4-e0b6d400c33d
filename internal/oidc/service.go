package oidc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/coreos/go-oidc/v3/oidc"
	"go.uber.org/zap"
	"golang.org/x/oauth2"
)

// OIDCClaims 表示OIDC令牌中的声明
type OIDCClaims struct {
	Subject    string   `json:"sub"`
	Name       string   `json:"name"`
	Email      string   `json:"email"`
	GivenName  string   `json:"given_name"`
	FamilyName string   `json:"family_name"`
	Picture    string   `json:"picture"`
	Groups     []string `json:"groups"`
	Raw        map[string]interface{}
}

// OIDCTokens 包含OAuth2和OIDC令牌
type OIDCTokens struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

// OIDCAuthResult 包含认证结果信息
type OIDCAuthResult struct {
	Tokens *OIDCTokens `json:"tokens"`
	Claims *OIDCClaims `json:"claims"`
}

// Service OIDC服务接口
type Service interface {
	// 获取OIDC登录URL
	GetAuthURL(state string) string

	// 处理OIDC回调
	HandleCallback(ctx context.Context, code string) (*OIDCAuthResult, error)

	// 验证并解析ID Token
	VerifyIDToken(ctx context.Context, rawIDToken string) (*OIDCClaims, error)

	// 刷新访问令牌
	RefreshToken(ctx context.Context, refreshToken string) (*OIDCTokens, error)

	// 更新OIDC配置
	UpdateConfig(config *OIDCConfig)
}

// ServiceImpl OIDC服务实现
type ServiceImpl struct {
	log          *zap.Logger
	config       *OIDCConfig
	provider     *oidc.Provider
	verifier     *oidc.IDTokenVerifier
	oauth2Config *oauth2.Config
}

// NewService 创建新的OIDC服务
func NewService(config *OIDCConfig, log *zap.Logger) (Service, error) {
	if config == nil {
		return nil, errors.New("OIDC配置不能为空")
	}

	service := &ServiceImpl{
		config: config,
		log:    log,
	}

	// 如果OIDC已启用，初始化提供者
	if config.Enabled && config.IssuerURL != "" {
		if err := service.initProvider(); err != nil {
			log.Warn("初始化OIDC提供者失败，OIDC功能将不可用", zap.Error(err))
		}
	}

	return service, nil
}

// initProvider 初始化OIDC提供者
func (s *ServiceImpl) initProvider() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	provider, err := oidc.NewProvider(ctx, s.config.IssuerURL)
	if err != nil {
		return fmt.Errorf("初始化OIDC提供者失败: %w", err)
	}

	// 设置ID令牌验证器
	s.provider = provider
	s.verifier = provider.Verifier(&oidc.Config{
		ClientID: s.config.ClientID,
	})

	// 解析作用域
	scopes := strings.Split(s.config.Scopes, ",")
	if len(scopes) == 0 || (len(scopes) == 1 && scopes[0] == "") {
		scopes = strings.Split(DefaultScopes, ",")
	}

	// 设置OAuth2配置
	s.oauth2Config = &oauth2.Config{
		ClientID:     s.config.ClientID,
		ClientSecret: s.config.ClientSecret,
		RedirectURL:  s.config.RedirectURI,
		Endpoint:     provider.Endpoint(),
		Scopes:       scopes,
	}

	s.log.Info("OIDC配置已成功更新并初始化", zap.Any("issuer", s.config.IssuerURL), zap.Any("client_id", s.config.ClientID), zap.Any("redirect_uri", s.config.RedirectURI))
	return nil
}

// GetAuthURL 生成OIDC认证URL
func (s *ServiceImpl) GetAuthURL(state string) string {
	if !s.config.Enabled || s.oauth2Config == nil {
		return ""
	}
	return s.oauth2Config.AuthCodeURL(state)
}

// HandleCallback 处理OIDC回调
func (s *ServiceImpl) HandleCallback(ctx context.Context, code string) (*OIDCAuthResult, error) {
	if !s.config.Enabled || s.oauth2Config == nil {
		return nil, errors.New("OIDC未启用或未正确配置")
	}

	// 确保OAuth2配置正确
	if s.oauth2Config.ClientID == "" || s.oauth2Config.ClientSecret == "" {
		return nil, errors.New("OIDC客户端ID或密钥未配置")
	}

	// 记录当前配置信息，帮助调试
	log := s.log
	if log != nil {
		log.Debug("OIDC回调处理",
			zap.String("client_id", s.oauth2Config.ClientID),
			zap.Bool("has_client_secret", s.oauth2Config.ClientSecret != ""),
			zap.String("redirect_uri", s.oauth2Config.RedirectURL))
	}

	// 交换code获取token
	oauth2Token, err := s.oauth2Config.Exchange(ctx, code)
	if err != nil {
		if log != nil {
			log.Error("获取OAuth2令牌失败",
				zap.Error(err),
				zap.String("client_id", s.oauth2Config.ClientID),
				zap.Bool("has_client_secret", s.oauth2Config.ClientSecret != ""))
		}
		return nil, fmt.Errorf("获取OAuth2令牌失败: %w", err)
	}

	// 从OAuth2令牌中提取ID令牌
	rawIDToken, ok := oauth2Token.Extra("id_token").(string)
	if !ok {
		return nil, errors.New("响应中没有id_token")
	}

	// 验证ID令牌
	claims, err := s.VerifyIDToken(ctx, rawIDToken)
	if err != nil {
		return nil, err
	}

	// 构建返回结果
	tokens := &OIDCTokens{
		AccessToken:  oauth2Token.AccessToken,
		RefreshToken: oauth2Token.RefreshToken,
		IDToken:      rawIDToken,
		TokenType:    oauth2Token.TokenType,
		ExpiresIn:    int(oauth2Token.Expiry.Sub(time.Now()).Seconds()),
	}

	return &OIDCAuthResult{
		Tokens: tokens,
		Claims: claims,
	}, nil
}

// VerifyIDToken 验证并解析ID令牌
func (s *ServiceImpl) VerifyIDToken(ctx context.Context, rawIDToken string) (*OIDCClaims, error) {
	if !s.config.Enabled || s.verifier == nil {
		return nil, errors.New("OIDC未启用或未正确配置")
	}

	// 验证ID令牌
	idToken, err := s.verifier.Verify(ctx, rawIDToken)
	if err != nil {
		return nil, fmt.Errorf("无效的ID令牌: %w", err)
	}

	// 解析声明
	var claims map[string]interface{}
	if err := idToken.Claims(&claims); err != nil {
		return nil, fmt.Errorf("解析ID令牌声明失败: %w", err)
	}

	// 提取标准和自定义声明
	result := &OIDCClaims{
		Subject: idToken.Subject,
		Raw:     claims,
	}

	// 基本信息
	if v, ok := claims["name"].(string); ok {
		result.Name = v
	}
	if v, ok := claims["email"].(string); ok {
		result.Email = v
	}
	if v, ok := claims["given_name"].(string); ok {
		result.GivenName = v
	}
	if v, ok := claims["family_name"].(string); ok {
		result.FamilyName = v
	}
	if v, ok := claims["picture"].(string); ok {
		result.Picture = v
	}

	// 组声明
	groupsClaim := s.config.GroupsClaim
	if groupsClaim == "" {
		groupsClaim = DefaultGroupsClaim
	}
	if groups, ok := claims[groupsClaim]; ok {
		result.Groups = s.parseStringArray(groups)
	}

	return result, nil
}

// parseStringArray 解析声明中的字符串数组
func (s *ServiceImpl) parseStringArray(value interface{}) []string {
	var result []string

	switch v := value.(type) {
	case []interface{}:
		for _, item := range v {
			if str, ok := item.(string); ok {
				result = append(result, str)
			}
		}
	case []string:
		result = v
	case string:
		// 尝试作为JSON数组解析
		var arr []string
		if err := json.Unmarshal([]byte(v), &arr); err == nil {
			result = arr
		} else {
			// 单个字符串作为一个元素
			result = []string{v}
		}
	}

	return result
}

// RefreshToken 刷新访问令牌
func (s *ServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*OIDCTokens, error) {
	if !s.config.Enabled || s.oauth2Config == nil {
		return nil, errors.New("OIDC未启用或未正确配置")
	}

	// 使用刷新令牌获取新的访问令牌
	tokenSource := s.oauth2Config.TokenSource(ctx, &oauth2.Token{
		RefreshToken: refreshToken,
	})

	oauth2Token, err := tokenSource.Token()
	if err != nil {
		return nil, fmt.Errorf("刷新令牌失败: %w", err)
	}

	// 从OAuth2令牌中提取ID令牌
	rawIDToken, _ := oauth2Token.Extra("id_token").(string)

	// 构建返回结果
	return &OIDCTokens{
		AccessToken:  oauth2Token.AccessToken,
		RefreshToken: oauth2Token.RefreshToken,
		IDToken:      rawIDToken,
		TokenType:    oauth2Token.TokenType,
		ExpiresIn:    int(oauth2Token.Expiry.Sub(time.Now()).Seconds()),
	}, nil
}

// UpdateConfig 更新OIDC配置
func (s *ServiceImpl) UpdateConfig(config *OIDCConfig) {
	if config == nil {
		s.log.Warn("尝试更新OIDC配置，但提供的配置为空")
		return
	}

	// 记录配置更新详情
	s.log.Info("正在更新OIDC配置",
		zap.Bool("enabled", config.Enabled),
		zap.String("issuer_url", config.IssuerURL),
		zap.String("client_id", config.ClientID),
		zap.Bool("has_client_secret", config.ClientSecret != ""),
		zap.String("redirect_uri", config.RedirectURI))

	// 检查关键配置是否有效
	if config.Enabled {
		if config.IssuerURL == "" {
			s.log.Error("OIDC配置无效：颁发者URL为空")
			return
		}
		if config.ClientID == "" {
			s.log.Error("OIDC配置无效：客户端ID为空")
			return
		}
		if config.ClientSecret == "" {
			s.log.Error("OIDC配置无效：客户端密钥为空")
			return
		}
		if config.RedirectURI == "" {
			s.log.Error("OIDC配置无效：回调URI为空")
			return
		}
	}

	// 检查客户端密钥是否被错误地设置为掩码
	if config.ClientSecret == "******" {
		s.log.Error("客户端密钥被错误地设置为掩码，无法更新OIDC配置")
		return
	}

	// 更新配置
	oldConfig := s.config
	s.config = config

	// 如果配置已启用，重新初始化提供者
	if config.Enabled && config.IssuerURL != "" {
		if err := s.initProvider(); err != nil {
			s.log.Error("更新OIDC配置后初始化提供者失败",
				zap.Error(err),
				zap.String("issuer", config.IssuerURL),
				zap.String("client_id", config.ClientID))

			// 如果初始化失败，回滚到旧配置
			if oldConfig != nil {
				s.config = oldConfig
				s.log.Info("由于初始化失败，OIDC配置已回滚到先前状态")

				// 尝试用旧配置重新初始化
				if oldConfig.Enabled && oldConfig.IssuerURL != "" {
					if initErr := s.initProvider(); initErr != nil {
						s.log.Error("使用旧配置重新初始化OIDC提供者也失败", zap.Error(initErr))
					} else {
						s.log.Info("使用旧配置重新初始化OIDC提供者成功")
					}
				}
			}
		} else {
			s.log.Info("OIDC配置已成功更新并初始化",
				zap.String("issuer", config.IssuerURL),
				zap.String("client_id", config.ClientID),
				zap.String("redirect_uri", config.RedirectURI))
		}
	} else if !config.Enabled {
		// OIDC已禁用，清理相关资源
		s.log.Info("OIDC已禁用，正在清理相关资源")
		s.provider = nil
		s.verifier = nil
		s.oauth2Config = nil
		s.log.Info("OIDC资源清理完成")
	}
}

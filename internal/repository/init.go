package repository

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"kubeops/internal/config"
	"kubeops/internal/model"
)

// Init 初始化数据库和仓储层
func Init(cfg *config.Config) (Repository, error) {
	db, err := setupDatabase(cfg)
	if err != nil {
		return nil, err
	}

	// 初始化默认用户
	if err := initDefaultUser(db); err != nil {
		return nil, err
	}
	return NewRepository(db), nil
}

// 设置数据库
func setupDatabase(cfg *config.Config) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	switch cfg.Database.Type {
	case "sqlite":
		db, err = gorm.Open(sqlite.Open(cfg.Database.DBName), &gorm.Config{})
	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Database.Type)
	}

	if err != nil {
		return nil, err
	}

	// 自动迁移模型
	err = db.AutoMigrate(
		&model.User{},
		&model.AuditLog{},
		&model.Cluster{},
		&model.Project{},
		&model.ProjectCluster{},
		&model.ProjectMember{},
		&model.Application{},
		&model.ApplicationCluster{},
		&model.ApplicationMember{},
		&model.Alert{},
		&model.RBACModel{},
		&model.SystemConfig{},
		&model.UserGroup{},
		&model.ApprovalFlow{},
		&model.ApprovalRequest{},
		&model.ApprovalRequestStep{},
		&model.AuditArchive{},
		&model.AuditArchiveConfig{},
		&model.KeycloakGroupMapping{},
		&model.UserGroupMember{},
		&model.ResourcePermission{},
		&model.UserResourcePermission{},
		&model.GroupResourcePermission{},
	)

	if err != nil {
		return nil, err
	}

	// 初始化RBAC模型数据
	initRBACModel(db)

	// 初始化系统配置
	initSystemConfigs(db)

	return db, nil
}

// 初始化RBAC模型数据
func initRBACModel(db *gorm.DB) {
	var count int64
	// 检查是否已有RBAC模型
	db.Model(&model.RBACModel{}).Count(&count)
	if count == 0 {
		// 创建默认RBAC模型
		defaultModel := model.DefaultRBACModel()
		db.Create(defaultModel)
	}
}

// 初始化系统配置
func initSystemConfigs(db *gorm.DB) {
	// 检查是否存在系统配置
	var count int64
	db.Model(&model.SystemConfig{}).Count(&count)

	// 如果不存在配置，创建默认配置
	if count == 0 {
		defaultConfig := &model.SystemConfig{
			// 飞书配置
			FeishuAppID:     "",
			FeishuAppSecret: "",

			// OBS配置
			OBSEnabled:       false,
			OBSEndpoint:      "https://obs.cn-north-4.myhuaweicloud.com",
			OBSAccessKey:     "",
			OBSSecretKey:     "",
			OBSBucket:        "kubeops-audit-logs",
			OBSRegion:        "cn-north-4",
			OBSEncryptionKey: "",

			// 审计配置
			AuditRetentionDays:   90,
			AuditArchiveInterval: "0",

			// 系统配置
			SystemName:      "KubeOps",
			SystemVersion:   "1.0.0",
			SystemDebugMode: false,
		}

		db.Create(defaultConfig)
	}
}

// 初始化默认用户
func initDefaultUser(db *gorm.DB) error {
	var count int64
	db.Model(&model.User{}).Count(&count)
	if count == 0 {
		hashpassword, _ := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
		// 创建默认管理员用户 - 按照technical-design.md 4.3.1节设计
		adminUser := &model.User{
			Username:         "admin",
			Password:         string(hashpassword),
			Email:            "<EMAIL>",
			Nickname:         "系统管理员",
			Status:           1,
			IdentityProvider: "local",
		}
		if err := db.Create(adminUser).Error; err != nil {
			return err
		}

		// 创建默认用户组 - 按照technical-design.md 4.3.2节设计
		adminGroup := &model.UserGroup{
			Name:        "admin",
			Description: "系统管理员用户组",
			Type:        "local",
		}
		if err := db.Create(adminGroup).Error; err != nil {
			return err
		}

		// 将用户添加到用户组 - 检查是否已存在关联
		var count int64
		if err := db.Model(&model.UserGroupMember{}).Where("user_id = ? AND group_id = ?", adminUser.ID, adminGroup.ID).Count(&count).Error; err != nil {
			return fmt.Errorf("检查用户组关联失败: %w", err)
		}

		if count == 0 {
			// 创建用户组关联 - 按照technical-design.md 4.3.3节设计
			member := &model.UserGroupMember{
				UserID:     adminUser.ID,
				GroupID:    adminGroup.ID,
				IsAutoJoin: false, // 手动添加
			}
			if err := db.Create(member).Error; err != nil {
				return fmt.Errorf("创建用户组关联失败: %w", err)
			}
			fmt.Printf("已将admin用户添加到admin用户组\n")
		} else {
			fmt.Printf("admin用户已在admin用户组中\n")
		}
	}
	return nil
}

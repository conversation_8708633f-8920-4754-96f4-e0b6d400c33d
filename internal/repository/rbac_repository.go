package repository

import (
	"context"

	"github.com/casbin/casbin/v2"
	"gorm.io/gorm"

	"kubeops/internal/model"
)

// RBACRepository RBAC统一仓储接口
// 包含权限管理、用户组权限、用户权限、RBAC模型等所有权限相关功能
type RBACRepository interface {
	// ==================== 权限资源管理 ====================
	// 权限CRUD
	ListPermissions(ctx context.Context, page, size int) ([]*model.ResourcePermission, int64, error)
	GetPermissionByID(ctx context.Context, id uint) (*model.ResourcePermission, error)
	GetPermissionByPath(ctx context.Context, path string) (*model.ResourcePermission, error)
	GetPermissionByPathAndActions(ctx context.Context, path, actions string) (*model.ResourcePermission, error)
	CreatePermission(ctx context.Context, permission *model.ResourcePermission) error
	UpdatePermission(ctx context.Context, permission *model.ResourcePermission) error
	DeletePermission(ctx context.Context, id uint) error
	BatchCreatePermissions(ctx context.Context, permissions []*model.ResourcePermission) error

	// ==================== 用户组权限管理 ====================
	GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error)
	AssignGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error
	RemoveGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error
	ClearGroupPermissions(ctx context.Context, groupID uint) error

	// ==================== 用户权限管理 ====================
	GetUserPermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error)
	AssignUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error
	RemoveUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error
	ClearUserPermissions(ctx context.Context, userID uint) error
	GrantUserPermission(ctx context.Context, userPermission *model.UserResourcePermission) error
	RevokeUserPermission(ctx context.Context, userID, permissionID uint) error
	GetUserDirectPermissions(ctx context.Context, userID uint) ([]*model.UserResourcePermission, error)
	GetUserEffectivePermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error)

	// ==================== RBAC模型管理 ====================
	GetActiveRBACModel(ctx context.Context) (*model.RBACModel, error)
	GetRBACModelByID(ctx context.Context, id uint) (*model.RBACModel, error)
	ListRBACModels(ctx context.Context) ([]*model.RBACModel, error)
	CreateRBACModel(ctx context.Context, model *model.RBACModel) error
	UpdateRBACModel(ctx context.Context, model *model.RBACModel) error
	DeleteRBACModel(ctx context.Context, id uint) error
	SetActiveRBACModel(ctx context.Context, id uint) error

	// ==================== 权限检查相关 ====================
	GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error)
	GetUserRolesInDomain(userID string, domain string) ([]string, error)

	// ==================== 批量操作 ====================
	BatchGrantGroupPermissions(ctx context.Context, groupPermissions []*model.GroupResourcePermission) error
	BatchGrantUserPermissions(ctx context.Context, userPermissions []*model.UserResourcePermission) error

	// ==================== Casbin集成 ====================
	GetEnforcer() *casbin.SyncedEnforcer
}

// rbacRepository RBAC统一仓储实现
type rbacRepository struct {
	db       *gorm.DB
	enforcer *casbin.SyncedEnforcer
}

// NewRBACRepository 创建RBAC仓储
func NewRBACRepository(db *gorm.DB, enforcer *casbin.SyncedEnforcer) RBACRepository {
	return &rbacRepository{
		db:       db,
		enforcer: enforcer,
	}
}

// ==================== 权限资源管理实现 ====================

// ListPermissions 获取权限列表
func (r *rbacRepository) ListPermissions(ctx context.Context, page, size int) ([]*model.ResourcePermission, int64, error) {
	var permissions []*model.ResourcePermission
	var total int64

	// 计算总数
	if err := r.db.WithContext(ctx).Model(&model.ResourcePermission{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * size
	if err := r.db.WithContext(ctx).Offset(offset).Limit(size).Find(&permissions).Error; err != nil {
		return nil, 0, err
	}

	return permissions, total, nil
}

// GetPermissionByID 根据ID获取权限
func (r *rbacRepository) GetPermissionByID(ctx context.Context, id uint) (*model.ResourcePermission, error) {
	var permission model.ResourcePermission
	err := r.db.WithContext(ctx).First(&permission, id).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// GetPermissionByPath 根据路径获取权限
func (r *rbacRepository) GetPermissionByPath(ctx context.Context, resourcePath string) (*model.ResourcePermission, error) {
	var permission model.ResourcePermission
	err := r.db.WithContext(ctx).Where("resource_path = ?", resourcePath).First(&permission).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// GetPermissionByPathAndActions 根据路径和操作获取权限
func (r *rbacRepository) GetPermissionByPathAndActions(ctx context.Context, resourcePath, actions string) (*model.ResourcePermission, error) {
	var permission model.ResourcePermission
	err := r.db.WithContext(ctx).Where("resource_path = ? AND actions = ?", resourcePath, actions).First(&permission).Error
	if err != nil {
		return nil, err
	}
	return &permission, nil
}

// CreatePermission 创建权限
func (r *rbacRepository) CreatePermission(ctx context.Context, permission *model.ResourcePermission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

// UpdatePermission 更新权限
func (r *rbacRepository) UpdatePermission(ctx context.Context, permission *model.ResourcePermission) error {
	return r.db.WithContext(ctx).Save(permission).Error
}

// DeletePermission 删除权限
func (r *rbacRepository) DeletePermission(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ResourcePermission{}, id).Error
}

// BatchCreatePermissions 批量创建权限
func (r *rbacRepository) BatchCreatePermissions(ctx context.Context, permissions []*model.ResourcePermission) error {
	return r.db.WithContext(ctx).CreateInBatches(permissions, 100).Error
}

// ==================== 用户组权限管理实现 ====================

// GetGroupPermissions 获取用户组权限
func (r *rbacRepository) GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	var permissions []*model.ResourcePermission
	err := r.db.WithContext(ctx).
		Joins("JOIN group_resource_permissions ON resource_permissions.id = group_resource_permissions.resource_permission_id").
		Where("group_resource_permissions.group_id = ?", groupID).
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

// AssignGroupPermissions 分配用户组权限
func (r *rbacRepository) AssignGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除现有权限
		if err := tx.Where("group_id = ?", groupID).Delete(&model.GroupResourcePermission{}).Error; err != nil {
			return err
		}

		// 添加新权限
		for _, permissionID := range permissionIDs {
			groupPermission := &model.GroupResourcePermission{
				GroupID:              groupID,
				ResourcePermissionID: permissionID,
			}
			if err := tx.Create(groupPermission).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// RemoveGroupPermissions 移除用户组权限
func (r *rbacRepository) RemoveGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error {
	return r.db.WithContext(ctx).
		Where("group_id = ? AND resource_permission_id IN ?", groupID, permissionIDs).
		Delete(&model.GroupResourcePermission{}).Error
}

// ClearGroupPermissions 清空用户组权限
func (r *rbacRepository) ClearGroupPermissions(ctx context.Context, groupID uint) error {
	return r.db.WithContext(ctx).Where("group_id = ?", groupID).Delete(&model.GroupResourcePermission{}).Error
}

// ==================== 用户权限管理实现 ====================

// GetUserPermissions 获取用户权限
func (r *rbacRepository) GetUserPermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error) {
	var permissions []*model.ResourcePermission
	err := r.db.WithContext(ctx).
		Joins("JOIN user_resource_permissions ON resource_permissions.id = user_resource_permissions.resource_permission_id").
		Where("user_resource_permissions.user_id = ?", userID).
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

// AssignUserPermissions 分配用户权限
func (r *rbacRepository) AssignUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除现有权限
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserResourcePermission{}).Error; err != nil {
			return err
		}

		// 添加新权限
		for _, permissionID := range permissionIDs {
			userPermission := &model.UserResourcePermission{
				UserID:               userID,
				ResourcePermissionID: permissionID,
			}
			if err := tx.Create(userPermission).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// RemoveUserPermissions 移除用户权限
func (r *rbacRepository) RemoveUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND resource_permission_id IN ?", userID, permissionIDs).
		Delete(&model.UserResourcePermission{}).Error
}

// ClearUserPermissions 清空用户权限
func (r *rbacRepository) ClearUserPermissions(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&model.UserResourcePermission{}).Error
}

// GrantUserPermission 授予用户权限
func (r *rbacRepository) GrantUserPermission(ctx context.Context, userPermission *model.UserResourcePermission) error {
	return r.db.WithContext(ctx).Create(userPermission).Error
}

// RevokeUserPermission 撤销用户权限
func (r *rbacRepository) RevokeUserPermission(ctx context.Context, userID, permissionID uint) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND resource_permission_id = ?", userID, permissionID).
		Delete(&model.UserResourcePermission{}).Error
}

// GetUserDirectPermissions 获取用户直接权限
func (r *rbacRepository) GetUserDirectPermissions(ctx context.Context, userID uint) ([]*model.UserResourcePermission, error) {
	var userPermissions []*model.UserResourcePermission
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&userPermissions).Error
	return userPermissions, err
}

// GetUserEffectivePermissions 获取用户有效权限（包括通过用户组继承的权限）
func (r *rbacRepository) GetUserEffectivePermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error) {
	var permissions []*model.ResourcePermission

	// 获取用户直接权限
	err := r.db.WithContext(ctx).
		Table("resource_permissions").
		Joins("JOIN user_resource_permissions ON resource_permissions.id = user_resource_permissions.resource_permission_id").
		Where("user_resource_permissions.user_id = ?", userID).
		Find(&permissions).Error

	if err != nil {
		return nil, err
	}

	// 获取用户组权限
	var groupPermissions []*model.ResourcePermission
	err = r.db.WithContext(ctx).
		Table("resource_permissions").
		Joins("JOIN group_resource_permissions ON resource_permissions.id = group_resource_permissions.resource_permission_id").
		Joins("JOIN user_group_members ON group_resource_permissions.group_id = user_group_members.group_id").
		Where("user_group_members.user_id = ?", userID).
		Find(&groupPermissions).Error

	if err != nil {
		return nil, err
	}

	// 合并权限（去重）
	permissionMap := make(map[uint]*model.ResourcePermission)
	for _, perm := range permissions {
		permissionMap[perm.ID] = perm
	}
	for _, perm := range groupPermissions {
		permissionMap[perm.ID] = perm
	}

	var result []*model.ResourcePermission
	for _, perm := range permissionMap {
		result = append(result, perm)
	}

	return result, nil
}

// ==================== RBAC模型管理实现 ====================

// GetActiveRBACModel 获取活动RBAC模型
func (r *rbacRepository) GetActiveRBACModel(ctx context.Context) (*model.RBACModel, error) {
	var rbacModel model.RBACModel
	err := r.db.WithContext(ctx).Where("is_active = ?", true).First(&rbacModel).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &rbacModel, nil
}

// GetRBACModelByID 通过ID获取RBAC模型
func (r *rbacRepository) GetRBACModelByID(ctx context.Context, id uint) (*model.RBACModel, error) {
	var rbacModel model.RBACModel
	err := r.db.WithContext(ctx).First(&rbacModel, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &rbacModel, nil
}

// ListRBACModels 获取所有RBAC模型
func (r *rbacRepository) ListRBACModels(ctx context.Context) ([]*model.RBACModel, error) {
	var models []*model.RBACModel
	err := r.db.WithContext(ctx).Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

// CreateRBACModel 创建RBAC模型
func (r *rbacRepository) CreateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error {
	return r.db.WithContext(ctx).Create(rbacModel).Error
}

// UpdateRBACModel 更新RBAC模型
func (r *rbacRepository) UpdateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error {
	return r.db.WithContext(ctx).Save(rbacModel).Error
}

// DeleteRBACModel 删除RBAC模型
func (r *rbacRepository) DeleteRBACModel(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.RBACModel{}, id).Error
}

// SetActiveRBACModel 设置活动RBAC模型
func (r *rbacRepository) SetActiveRBACModel(ctx context.Context, id uint) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先将所有模型设置为非活动
		if err := tx.Model(&model.RBACModel{}).Update("is_active", false).Error; err != nil {
			return err
		}

		// 将指定模型设置为活动
		if err := tx.Model(&model.RBACModel{}).Where("id = ?", id).Update("is_active", true).Error; err != nil {
			return err
		}

		return nil
	})
}

// ==================== 权限检查相关实现 ====================

// GetUserGroups 获取用户所属的用户组
func (r *rbacRepository) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	var groups []*model.UserGroup
	err := r.db.WithContext(ctx).
		Joins("JOIN user_group_members ON user_groups.id = user_group_members.group_id").
		Where("user_group_members.user_id = ?", userID).
		Find(&groups).Error
	if err != nil {
		return nil, err
	}
	return groups, nil
}

// GetUserRolesInDomain 获取用户在指定域中的角色
func (r *rbacRepository) GetUserRolesInDomain(userID string, domain string) ([]string, error) {
	if r.enforcer == nil {
		return nil, ErrNotImplemented
	}

	// 使用Casbin获取用户角色
	roles := r.enforcer.GetRolesForUserInDomain(userID, domain)
	return roles, nil
}

// ==================== 批量操作实现 ====================

// BatchGrantGroupPermissions 批量授予用户组权限
func (r *rbacRepository) BatchGrantGroupPermissions(ctx context.Context, groupPermissions []*model.GroupResourcePermission) error {
	return r.db.WithContext(ctx).CreateInBatches(groupPermissions, 100).Error
}

// BatchGrantUserPermissions 批量授予用户权限
func (r *rbacRepository) BatchGrantUserPermissions(ctx context.Context, userPermissions []*model.UserResourcePermission) error {
	return r.db.WithContext(ctx).CreateInBatches(userPermissions, 100).Error
}

// ==================== Casbin集成实现 ====================

// GetEnforcer 获取Casbin执行器
func (r *rbacRepository) GetEnforcer() *casbin.SyncedEnforcer {
	return r.enforcer
}

package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"
)

// clusterService 实现了ClusterService接口
type clusterService struct {
	repo           repository.Repository
	clusterManager *k8s.ClusterManager
	logger         logger.LoggerService
	tracer         trace.Tracer
}

// NewClusterService 创建集群服务实例
func NewClusterService(repo repository.Repository, clusterManager *k8s.ClusterManager, loggerSvc logger.LoggerService, tracer trace.Tracer) ClusterService {
	return &clusterService{
		repo:           repo,
		clusterManager: clusterManager,
		logger:         loggerSvc,
		tracer:         tracer,
	}
}

// CreateCluster 创建集群
func (s *clusterService) CreateCluster(ctx context.Context, req *model.ClusterCreateRequest) (*model.Cluster, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.CreateCluster")
	defer span.End()
	span.SetAttributes(attribute.String("cluster.name", req.Name))

	// 检查集群名称是否已存在
	existingCluster, err := s.repo.Cluster().GetClusterByName(ctx, req.Name)
	if err == nil && existingCluster != nil {
		return nil, fmt.Errorf("cluster name already exists: %s", req.Name)
	}

	// 验证kubeconfig有效性
	clusterInfo, err := s.TestClusterConnection(ctx, req.KubeConfig)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("cluster connection validation failed: %w", err)
	}

	// 创建集群对象
	cluster := &model.Cluster{
		Name:            req.Name,
		DisplayName:     req.DisplayName,
		Description:     req.Description,
		KubeConfig:      req.KubeConfig,
		Status:          model.ClusterStatusOnline,
		Version:         clusterInfo.Version,
		NodeCount:       clusterInfo.NodeCount,
		PodCount:        clusterInfo.PodCount,
		NamespaceCount:  clusterInfo.NamespaceCount,
		LastHealthCheck: time.Now(),
	}

	err = s.repo.Cluster().CreateCluster(ctx, cluster)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// 添加到集群管理器
	if err := s.addClusterToManager(cluster); err != nil {
		s.logger.Error("Failed to add cluster to manager", zap.Error(err), zap.String("cluster", cluster.Name))
	}

	// 启动Informer
	if err := s.StartClusterInformers(ctx, cluster.ID); err != nil {
		s.logger.Error("Failed to start cluster informers", zap.Error(err), zap.Uint("cluster_id", cluster.ID))
	}

	return cluster, nil
}

// GetClusterByID 根据ID获取集群
func (s *clusterService) GetClusterByID(ctx context.Context, id uint) (*model.Cluster, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterByID")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(id)))

	return s.repo.Cluster().GetClusterByID(ctx, id)
}

// GetClusterByName 根据名称获取集群
func (s *clusterService) GetClusterByName(ctx context.Context, name string) (*model.Cluster, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterByName")
	defer span.End()
	span.SetAttributes(attribute.String("cluster.name", name))

	return s.repo.Cluster().GetClusterByName(ctx, name)
}

// UpdateCluster 更新集群
func (s *clusterService) UpdateCluster(ctx context.Context, id uint, req *model.ClusterUpdateRequest) (*model.Cluster, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.UpdateCluster")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(id)))

	// 检查集群是否存在
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, fmt.Errorf("cluster not found")
	}

	// 更新字段
	if req.DisplayName != "" {
		cluster.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		cluster.Description = req.Description
	}

	// 如果更新了kubeconfig，需要验证连接
	if req.KubeConfig != "" {
		clusterInfo, err := s.TestClusterConnection(ctx, req.KubeConfig)
		if err != nil {
			span.RecordError(err)
			return nil, fmt.Errorf("cluster connection validation failed: %w", err)
		}

		cluster.KubeConfig = req.KubeConfig
		cluster.Version = clusterInfo.Version
		cluster.NodeCount = clusterInfo.NodeCount
		cluster.PodCount = clusterInfo.PodCount
		cluster.NamespaceCount = clusterInfo.NamespaceCount
		cluster.LastHealthCheck = time.Now()

		// 重新启动Informer
		s.RestartClusterInformers(ctx, cluster.ID)
	}

	err = s.repo.Cluster().UpdateCluster(ctx, cluster)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return cluster, nil
}

// DeleteCluster 删除集群
func (s *clusterService) DeleteCluster(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.DeleteCluster")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(id)))

	// 检查集群是否存在
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if cluster == nil {
		return fmt.Errorf("cluster not found")
	}

	// 停止Informer
	s.StopClusterInformers(ctx, id)

	// 从集群管理器中移除
	if err := s.clusterManager.RemoveCluster(cluster.Name); err != nil {
		s.logger.Error("Failed to remove cluster from manager", zap.Error(err), zap.String("cluster", cluster.Name))
	}

	return s.repo.Cluster().DeleteCluster(ctx, id)
}

// ListClusters 获取集群列表
func (s *clusterService) ListClusters(ctx context.Context, page, pageSize int) ([]*model.Cluster, int64, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.ListClusters")
	defer span.End()
	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("pageSize", pageSize),
	)

	return s.repo.Cluster().ListClustersPaginated(ctx, page, pageSize)
}

// TestClusterConnection 测试集群连接
func (s *clusterService) TestClusterConnection(ctx context.Context, kubeconfig string) (*model.ClusterInfo, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.TestClusterConnection")
	defer span.End()

	// 解码kubeconfig（如果是base64编码）
	kubeconfigData := kubeconfig
	if decoded, err := base64.StdEncoding.DecodeString(kubeconfig); err == nil {
		kubeconfigData = string(decoded)
	}

	// 解析kubeconfig
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfigData))
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("parse kubeconfig failed: %w", err)
	}

	// 创建临时客户端
	tempClient, err := k8s.NewClientFromConfig(config)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("create k8s client failed: %w", err)
	}
	defer tempClient.Close()

	// 获取集群信息
	clusterInfo, err := s.getClusterInfoFromClient(ctx, tempClient)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster info failed: %w", err)
	}

	return clusterInfo, nil
}

// HealthCheckCluster 健康检查集群
func (s *clusterService) HealthCheckCluster(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.HealthCheckCluster")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if cluster == nil {
		return fmt.Errorf("cluster not found")
	}

	// 获取K8s客户端
	k8sClient, err := s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		// 更新集群状态为离线
		s.repo.Cluster().UpdateClusterStatus(ctx, clusterID, model.ClusterStatusOffline)
		span.RecordError(err)
		return fmt.Errorf("cluster connection failed: %w", err)
	}

	// 尝试获取集群版本信息
	_, err = k8sClient.GetClient().Discovery().ServerVersion()
	if err != nil {
		// 更新集群状态为离线
		s.repo.Cluster().UpdateClusterStatus(ctx, clusterID, model.ClusterStatusOffline)
		span.RecordError(err)
		return fmt.Errorf("cluster health check failed: %w", err)
	}

	// 更新集群状态为在线
	s.repo.Cluster().UpdateClusterStatus(ctx, clusterID, model.ClusterStatusOnline)
	return nil
}

// GetClusterInfo 获取集群信息
func (s *clusterService) GetClusterInfo(ctx context.Context, clusterID uint) (*model.ClusterInfo, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterInfo")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, fmt.Errorf("cluster not found")
	}

	// 获取K8s客户端
	k8sClient, err := s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster client failed: %w", err)
	}

	// 获取集群信息
	clusterInfo, err := s.getClusterInfoFromClient(ctx, k8sClient)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster info failed: %w", err)
	}

	return clusterInfo, nil
}

// UpdateClusterStatus 更新集群状态
func (s *clusterService) UpdateClusterStatus(ctx context.Context, clusterID uint, status model.ClusterStatus) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.UpdateClusterStatus")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.Int("cluster.status", int(status)),
	)

	return s.repo.Cluster().UpdateClusterStatus(ctx, clusterID, status)
}

// StartClusterInformers 启动集群Informer
func (s *clusterService) StartClusterInformers(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.StartClusterInformers")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if cluster == nil {
		return fmt.Errorf("cluster not found")
	}

	// 获取K8s客户端
	_, err = s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("get cluster client failed: %w", err)
	}

	// TODO: 启动Informer
	// 这里需要实现Informer启动逻辑
	// 1. 创建SharedInformerFactory
	// 2. 启动各种资源的Informer
	// 3. 设置事件处理器
	// 4. 启动同步

	s.logger.Info("Cluster informers started", zap.Uint("cluster_id", clusterID), zap.String("cluster", cluster.Name))
	return nil
}

// StopClusterInformers 停止集群Informer
func (s *clusterService) StopClusterInformers(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.StopClusterInformers")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// TODO: 停止Informer
	// 这里需要实现Informer停止逻辑

	s.logger.Info("Cluster informers stopped", zap.Uint("cluster_id", clusterID))
	return nil
}

// RestartClusterInformers 重启集群Informer
func (s *clusterService) RestartClusterInformers(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ClusterService.RestartClusterInformers")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 先停止
	if err := s.StopClusterInformers(ctx, clusterID); err != nil {
		s.logger.Error("Failed to stop cluster informers", zap.Error(err), zap.Uint("cluster_id", clusterID))
	}

	// 再启动
	return s.StartClusterInformers(ctx, clusterID)
}

// GetInformerStatus 获取Informer状态
func (s *clusterService) GetInformerStatus(ctx context.Context, clusterID uint) (map[string]interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetInformerStatus")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// TODO: 获取Informer状态
	// 这里需要实现Informer状态查询逻辑

	status := map[string]interface{}{
		"cluster_id": clusterID,
		"status":     "running",
		"started_at": time.Now(),
		"resources": map[string]interface{}{
			"pods":        map[string]interface{}{"synced": true, "count": 0},
			"services":    map[string]interface{}{"synced": true, "count": 0},
			"deployments": map[string]interface{}{"synced": true, "count": 0},
		},
	}

	return status, nil
}

// GetClusterResources 获取集群资源
func (s *clusterService) GetClusterResources(ctx context.Context, clusterID uint, resourceType string) (interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterResources")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.String("resource.type", resourceType),
	)

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, fmt.Errorf("cluster not found")
	}

	// 获取K8s客户端
	_, err = s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster client failed: %w", err)
	}

	// TODO: 根据resourceType获取对应资源
	// 这里需要实现具体的资源获取逻辑

	return nil, fmt.Errorf("resource type %s not implemented", resourceType)
}

// GetClusterEvents 获取集群事件
func (s *clusterService) GetClusterEvents(ctx context.Context, clusterID uint, namespace string) (interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterEvents")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.String("namespace", namespace),
	)

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, fmt.Errorf("cluster not found")
	}

	// 获取K8s客户端
	_, err = s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster client failed: %w", err)
	}

	// TODO: 获取集群事件
	// 这里需要实现事件获取逻辑

	return nil, fmt.Errorf("get cluster events not implemented")
}

// GetClusterMetrics 获取集群指标
func (s *clusterService) GetClusterMetrics(ctx context.Context, clusterID uint) (map[string]interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ClusterService.GetClusterMetrics")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 获取集群
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, fmt.Errorf("cluster not found")
	}

	// TODO: 获取集群指标
	// 这里需要实现指标获取逻辑，可能需要集成Prometheus

	metrics := map[string]interface{}{
		"cluster_id":            clusterID,
		"cpu_usage_percent":     0.0,
		"memory_usage_percent":  0.0,
		"storage_usage_percent": 0.0,
		"pod_count":             cluster.PodCount,
		"node_count":            cluster.NodeCount,
		"namespace_count":       cluster.NamespaceCount,
		"collected_at":          time.Now(),
	}

	return metrics, nil
}

// EnableCluster 启用集群
func (s *clusterService) EnableCluster(ctx context.Context, id uint) error {
	return s.UpdateClusterStatus(ctx, id, model.ClusterStatusOnline)
}

// DisableCluster 禁用集群
func (s *clusterService) DisableCluster(ctx context.Context, id uint) error {
	return s.UpdateClusterStatus(ctx, id, model.ClusterStatusOffline)
}

// SetMaintenanceMode 设置维护模式
func (s *clusterService) SetMaintenanceMode(ctx context.Context, id uint) error {
	return s.UpdateClusterStatus(ctx, id, model.ClusterStatusMaintenance)
}

// ExitMaintenanceMode 退出维护模式
func (s *clusterService) ExitMaintenanceMode(ctx context.Context, id uint) error {
	return s.UpdateClusterStatus(ctx, id, model.ClusterStatusOnline)
}

// addClusterToManager 将集群添加到管理器
func (s *clusterService) addClusterToManager(cluster *model.Cluster) error {
	// 解码kubeconfig（如果是base64编码）
	kubeconfigData := cluster.KubeConfig
	if decoded, err := base64.StdEncoding.DecodeString(cluster.KubeConfig); err == nil {
		kubeconfigData = string(decoded)
	}

	// 添加到集群管理器
	return s.clusterManager.AddClusterFromConfig(cluster.Name, kubeconfigData)
}

// getClusterInfoFromClient 从K8s客户端获取集群信息
func (s *clusterService) getClusterInfoFromClient(ctx context.Context, k8sClient k8s.K8sClient) (*model.ClusterInfo, error) {
	// 获取集群版本
	version, err := k8sClient.GetClient().Discovery().ServerVersion()
	if err != nil {
		return nil, fmt.Errorf("get server version failed: %w", err)
	}

	// 获取节点数量
	nodes, err := k8sClient.GetClient().CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("list nodes failed: %w", err)
	}

	// 获取Pod数量
	pods, err := k8sClient.GetClient().CoreV1().Pods("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("list pods failed: %w", err)
	}

	// 获取命名空间数量
	namespaces, err := k8sClient.GetClient().CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("list namespaces failed: %w", err)
	}

	clusterInfo := &model.ClusterInfo{
		Version:        version.GitVersion,
		NodeCount:      len(nodes.Items),
		PodCount:       len(pods.Items),
		NamespaceCount: len(namespaces.Items),
	}

	return clusterInfo, nil
}

// CreateCluster 创建集群

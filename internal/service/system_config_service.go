package service

import (
	"context"
	"fmt"
	"sync"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/oidc"
	"kubeops/internal/repository"
	"kubeops/pkg/lark"
	"kubeops/pkg/obs"
)

// SystemConfigService 系统配置服务接口
type SystemConfigService interface {
	// 获取OIDC配置
	GetOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error)
	// 更新OIDC配置
	UpdateOIDCConfig(ctx context.Context, config *model.SystemOIDCConfig) error
	// 获取原始OIDC配置
	GetRawOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error)
	// 测试OIDC配置
	TestOIDCConfig(ctx context.Context, config *model.SystemOIDCConfig) error

	// 获取飞书配置
	GetFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error)
	// 更新飞书配置
	UpdateFeishuConfig(ctx context.Context, config *model.SystemFeishuConfig) error
	// 获取原始飞书配置
	GetRawFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error)
	// 测试飞书配置
	TestFeishuConfig(ctx context.Context, config *model.SystemFeishuConfig) error

	// 获取OBS配置
	GetOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error)
	// 更新OBS配置
	UpdateOBSConfig(ctx context.Context, config *model.SystemOBSConfig) error
	// 获取原始OBS配置
	GetRawOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error)
	// 测试OBS配置
	TestOBSConfig(ctx context.Context, config *model.SystemOBSConfig) error

	// 获取审计配置
	GetAuditConfig(ctx context.Context) (*model.SystemAuditConfig, error)
	// 更新审计配置
	UpdateAuditConfig(ctx context.Context, config *model.SystemAuditConfig) error

	// 获取基本配置
	GetBasicConfig(ctx context.Context) (*model.SystemBasicConfig, error)
	// 更新基本配置
	UpdateBasicConfig(ctx context.Context, config *model.SystemBasicConfig) error
}

// SystemConfigServiceImpl 系统配置服务实现
type SystemConfigServiceImpl struct {
	repo           repository.Repository
	logger         logger.LoggerService
	tracer         trace.Tracer
	configHandlers *ConfigUpdateHandlers
	handlersLock   sync.RWMutex // 保护configHandlers的读写锁
	configLock     sync.RWMutex // 保护配置读写的读写锁
}

// MaskSensitiveData 对敏感数据进行掩码处理
func MaskSensitiveData(data string) string {
	if data == "" {
		return ""
	}
	return "******"
}

// TestOIDCConfig 测试OIDC配置
func (s *SystemConfigServiceImpl) TestOIDCConfig(ctx context.Context, config *model.SystemOIDCConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.TestOIDCConfig")
	defer span.End()

	// 这里只是一个简单的实现，实际应该尝试连接OIDC提供商
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	if config.IssuerURL == "" {
		return fmt.Errorf("IssuerURL不能为空")
	}

	if config.ClientID == "" {
		return fmt.Errorf("ClientID不能为空")
	}

	if config.ClientSecret == "" {
		return fmt.Errorf("ClientSecret不能为空")
	}

	return nil
}

// TestFeishuConfig 测试飞书配置
func (s *SystemConfigServiceImpl) TestFeishuConfig(ctx context.Context, config *model.SystemFeishuConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.TestFeishuConfig")
	defer span.End()

	// 这里只是一个简单的实现，实际应该尝试连接飞书API
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	if config.AppID == "" {
		return fmt.Errorf("AppID不能为空")
	}

	if config.AppSecret == "" {
		return fmt.Errorf("AppSecret不能为空")
	}

	return nil
}

// TestOBSConfig 测试OBS配置
func (s *SystemConfigServiceImpl) TestOBSConfig(ctx context.Context, config *model.SystemOBSConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.TestOBSConfig")
	defer span.End()

	// 这里只是一个简单的实现，实际应该尝试连接OBS服务
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	if !config.Enabled {
		return nil
	}

	if config.Endpoint == "" {
		return fmt.Errorf("Endpoint不能为空")
	}

	if config.AccessKey == "" {
		return fmt.Errorf("AccessKey不能为空")
	}

	if config.SecretKey == "" {
		return fmt.Errorf("SecretKey不能为空")
	}

	if config.Bucket == "" {
		return fmt.Errorf("Bucket不能为空")
	}

	return nil
}

// SetConfigUpdateHandlers 设置配置更新处理器
func (s *SystemConfigServiceImpl) SetConfigUpdateHandlers(handlers *ConfigUpdateHandlers) {
	s.handlersLock.Lock()
	defer s.handlersLock.Unlock()

	s.configHandlers = handlers
}

// 获取当前配置处理器的安全方法
func (s *SystemConfigServiceImpl) getConfigHandlers() *ConfigUpdateHandlers {
	s.handlersLock.RLock()
	defer s.handlersLock.RUnlock()

	return s.configHandlers
}

// NewSystemConfigService 创建系统配置服务
func NewSystemConfigService(
	repo repository.Repository,
	loggerSvc logger.LoggerService,
	tracer trace.Tracer,
) SystemConfigService {
	return &SystemConfigServiceImpl{
		repo:   repo,
		logger: loggerSvc,
		tracer: tracer,
	}
}

// GetBasicConfig 获取基本系统配置
func (s *SystemConfigServiceImpl) GetBasicConfig(ctx context.Context) (*model.SystemBasicConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetBasicConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "basic")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	return config.GetBasicConfig(), nil
}

// UpdateBasicConfig 更新基本系统配置
func (s *SystemConfigServiceImpl) UpdateBasicConfig(ctx context.Context, config *model.SystemBasicConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateBasicConfig")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "basic")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 更新基本配置
	sysConfig.UpdateFromBasicConfig(config)

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	return nil
}

// GetFeishuConfig 获取飞书配置
func (s *SystemConfigServiceImpl) GetFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetFeishuConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "feishu")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	feishuConfig := config.GetFeishuConfig()
	// 掩码处理敏感信息
	if feishuConfig.AppSecret != "" {
		feishuConfig.AppSecret = MaskSensitiveData(feishuConfig.AppSecret)
	}

	return feishuConfig, nil
}

// UpdateFeishuConfig 更新飞书配置
func (s *SystemConfigServiceImpl) UpdateFeishuConfig(ctx context.Context, config *model.SystemFeishuConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateFeishuConfig")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "feishu")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 记录更新前的配置信息
	s.logger.Debug("更新飞书配置",
		zap.String("old_app_id", sysConfig.FeishuAppID),
		zap.Bool("has_old_app_secret", sysConfig.FeishuAppSecret != ""),
		zap.String("new_app_id", config.AppID),
		zap.Bool("new_app_secret_is_mask", config.AppSecret == "******"))

	// 特殊处理应用密钥
	oldAppSecret := sysConfig.FeishuAppSecret

	// 更新飞书配置
	sysConfig.UpdateFromFeishuConfig(config)

	// 检查应用密钥是否被错误地设置为掩码
	if sysConfig.FeishuAppSecret == "******" {
		s.logger.Warn("检测到飞书应用密钥被错误地设置为掩码，恢复为原始值")
		sysConfig.FeishuAppSecret = oldAppSecret
	}

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	// 通知配置更新 - 获取当前处理器的安全引用
	handlers := s.getConfigHandlers()
	if handlers != nil && config.AppID != "" {
		// 使用实际保存在数据库中的值，而不是可能的掩码值
		feishuConfig := &lark.FeishuConfig{
			AppID:     sysConfig.FeishuAppID,
			AppSecret: sysConfig.FeishuAppSecret,
		}

		s.logger.Info("正在更新飞书服务配置",
			zap.String("app_id", feishuConfig.AppID),
			zap.Bool("has_app_secret", feishuConfig.AppSecret != ""))

		// 确保应用密钥不为空
		if feishuConfig.AppSecret == "" {
			s.logger.Warn("飞书应用密钥为空，这可能导致飞书集成功能不可用")
		}

		// 处理器已经实现了内部的线程安全
		handlers.HandleFeishuConfigUpdate(feishuConfig)
	}

	return nil
}

// GetOIDCConfig 获取OIDC配置（敏感信息掩码处理）
func (s *SystemConfigServiceImpl) GetOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetOIDCConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "oidc")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	oidcConfig := config.GetOIDCConfig()
	// 掩码处理敏感信息
	if oidcConfig.ClientSecret != "" {
		oidcConfig.ClientSecret = MaskSensitiveData(oidcConfig.ClientSecret)
	}

	return oidcConfig, nil
}

// GetRawOIDCConfig 获取OIDC配置（不进行掩码处理，仅供内部使用）
func (s *SystemConfigServiceImpl) GetRawOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetRawOIDCConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "oidc")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	oidcConfig := config.GetOIDCConfig()
	// 不进行掩码处理，返回原始值
	return oidcConfig, nil
}

// UpdateOIDCConfig 更新OIDC配置
func (s *SystemConfigServiceImpl) UpdateOIDCConfig(ctx context.Context, config *model.SystemOIDCConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateOIDCConfig")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "oidc")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 记录更新前的配置信息
	s.logger.Debug("更新OIDC配置",
		zap.Bool("old_enabled", sysConfig.OIDCEnabled),
		zap.String("old_issuer_url", sysConfig.OIDCIssuerURL),
		zap.String("old_client_id", sysConfig.OIDCClientID),
		zap.Bool("has_old_client_secret", sysConfig.OIDCClientSecret != ""),
		zap.String("old_redirect_uri", sysConfig.OIDCRedirectURI),
		zap.Bool("new_enabled", config.Enabled),
		zap.String("new_issuer_url", config.IssuerURL),
		zap.String("new_client_id", config.ClientID),
		zap.Bool("new_client_secret_is_mask", config.ClientSecret == "******"),
		zap.String("new_redirect_uri", config.RedirectURI))

	// 特殊处理客户端密钥
	oldClientSecret := sysConfig.OIDCClientSecret

	// 更新OIDC配置
	sysConfig.UpdateFromOIDCConfig(config)

	// 检查客户端密钥是否被错误地设置为掩码
	if sysConfig.OIDCClientSecret == "******" {
		s.logger.Warn("检测到客户端密钥被错误地设置为掩码，恢复为原始值")
		sysConfig.OIDCClientSecret = oldClientSecret
	}

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	// 通知配置更新 - 获取当前处理器的安全引用
	handlers := s.getConfigHandlers()
	if handlers != nil && config.Enabled {
		// 使用实际保存在数据库中的值，而不是可能的掩码值
		oidcConfig := &oidc.OIDCConfig{
			Enabled:      sysConfig.OIDCEnabled,
			IssuerURL:    sysConfig.OIDCIssuerURL,
			ClientID:     sysConfig.OIDCClientID,
			ClientSecret: sysConfig.OIDCClientSecret, // 使用实际保存的密钥，而不是可能的掩码值
			RedirectURI:  sysConfig.OIDCRedirectURI,
			Scopes:       sysConfig.OIDCScopes,
			GroupsClaim:  sysConfig.OIDCGroupsClaim,
		}

		s.logger.Info("正在更新OIDC服务配置",
			zap.Bool("enabled", oidcConfig.Enabled),
			zap.String("issuer_url", oidcConfig.IssuerURL),
			zap.String("client_id", oidcConfig.ClientID),
			zap.Bool("has_client_secret", oidcConfig.ClientSecret != ""),
			zap.String("redirect_uri", oidcConfig.RedirectURI))

		// 确保客户端密钥不为空
		if oidcConfig.ClientSecret == "" {
			s.logger.Error("OIDC客户端密钥为空，这可能导致认证失败")
		}

		handlers.HandleOIDCConfigUpdate(oidcConfig)
	}

	return nil
}

// GetOBSConfig 获取OBS配置
func (s *SystemConfigServiceImpl) GetOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetOBSConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "obs")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	obsConfig := config.GetOBSConfig()
	// 掩码处理敏感信息
	if obsConfig.SecretKey != "" {
		obsConfig.SecretKey = MaskSensitiveData(obsConfig.SecretKey)
	}
	if obsConfig.EncryptionKey != "" {
		obsConfig.EncryptionKey = MaskSensitiveData(obsConfig.EncryptionKey)
	}

	return obsConfig, nil
}

// UpdateOBSConfig 更新OBS配置
func (s *SystemConfigServiceImpl) UpdateOBSConfig(ctx context.Context, config *model.SystemOBSConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateOBSConfig")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "obs")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 记录更新前的配置信息
	s.logger.Debug("更新OBS配置",
		zap.Bool("old_enabled", sysConfig.OBSEnabled),
		zap.String("old_endpoint", sysConfig.OBSEndpoint),
		zap.String("old_access_key", sysConfig.OBSAccessKey),
		zap.Bool("has_old_secret_key", sysConfig.OBSSecretKey != ""),
		zap.String("old_bucket", sysConfig.OBSBucket),
		zap.Bool("has_old_encryption_key", sysConfig.OBSEncryptionKey != ""),
		zap.Bool("new_enabled", config.Enabled),
		zap.String("new_endpoint", config.Endpoint),
		zap.String("new_access_key", config.AccessKey),
		zap.Bool("new_secret_key_is_mask", config.SecretKey == "******"),
		zap.String("new_bucket", config.Bucket),
		zap.Bool("new_encryption_key_is_mask", config.EncryptionKey == "******"))

	// 特殊处理密钥
	oldSecretKey := sysConfig.OBSSecretKey
	oldEncryptionKey := sysConfig.OBSEncryptionKey

	// 更新OBS配置
	sysConfig.UpdateFromOBSConfig(config)

	// 检查密钥是否被错误地设置为掩码
	if sysConfig.OBSSecretKey == "******" {
		s.logger.Warn("检测到OBS Secret Key被错误地设置为掩码，恢复为原始值")
		sysConfig.OBSSecretKey = oldSecretKey
	}
	if sysConfig.OBSEncryptionKey == "******" {
		s.logger.Warn("检测到OBS Encryption Key被错误地设置为掩码，恢复为原始值")
		sysConfig.OBSEncryptionKey = oldEncryptionKey
	}

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	// 通知配置更新 - 获取当前处理器的安全引用
	handlers := s.getConfigHandlers()
	if handlers != nil {
		// 使用实际保存在数据库中的值，而不是可能的掩码值
		obsConfig := &obs.OBSConfig{
			Enabled:       sysConfig.OBSEnabled,
			Endpoint:      sysConfig.OBSEndpoint,
			AccessKey:     sysConfig.OBSAccessKey,
			SecretKey:     sysConfig.OBSSecretKey,
			Bucket:        sysConfig.OBSBucket,
			Region:        sysConfig.OBSRegion,
			EncryptionKey: sysConfig.OBSEncryptionKey,
		}

		s.logger.Info("正在更新OBS服务配置",
			zap.Bool("enabled", obsConfig.Enabled),
			zap.String("endpoint", obsConfig.Endpoint),
			zap.String("access_key", obsConfig.AccessKey),
			zap.Bool("has_secret_key", obsConfig.SecretKey != ""),
			zap.String("bucket", obsConfig.Bucket),
			zap.Bool("has_encryption_key", obsConfig.EncryptionKey != ""))

		// 确保密钥不为空
		if obsConfig.Enabled && obsConfig.SecretKey == "" {
			s.logger.Warn("OBS Secret Key为空，这可能导致OBS功能不可用")
		}

		handlers.HandleOBSConfigUpdate(obsConfig)
	}

	return nil
}

// GetAuditConfig 获取审计配置
func (s *SystemConfigServiceImpl) GetAuditConfig(ctx context.Context) (*model.SystemAuditConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetAuditConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "audit")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	return config.GetAuditConfig(), nil
}

// UpdateAuditConfig 更新审计配置
func (s *SystemConfigServiceImpl) UpdateAuditConfig(ctx context.Context, config *model.SystemAuditConfig) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateAuditConfig")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "audit")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 更新审计配置
	sysConfig.UpdateFromAuditConfig(config)

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	return nil
}

// GetSystemName 获取系统名称
func (s *SystemConfigServiceImpl) GetSystemName(ctx context.Context) (string, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetSystemName")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "basic")
	if err != nil {
		return "", fmt.Errorf("获取系统配置失败: %w", err)
	}

	return config.SystemName, nil
}

// UpdateSystemName 更新系统名称
func (s *SystemConfigServiceImpl) UpdateSystemName(ctx context.Context, name string) error {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.UpdateSystemName")
	defer span.End()

	s.configLock.Lock()
	defer s.configLock.Unlock()

	// 获取当前配置
	sysConfig, err := s.repo.SystemConfig().GetSystemConfig(ctx, "basic")
	if err != nil {
		return fmt.Errorf("获取系统配置失败: %w", err)
	}

	// 更新系统名称
	sysConfig.SystemName = name

	// 保存配置
	if err := s.repo.SystemConfig().UpdateSystemConfig(ctx, sysConfig); err != nil {
		return fmt.Errorf("更新系统配置失败: %w", err)
	}

	return nil
}

// GetRawFeishuConfig 获取飞书配置（不进行掩码处理，仅供内部使用）
func (s *SystemConfigServiceImpl) GetRawFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetRawFeishuConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "feishu")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	feishuConfig := config.GetFeishuConfig()
	// 不进行掩码处理，返回原始值
	return feishuConfig, nil
}

// GetRawOBSConfig 获取OBS配置（不进行掩码处理，仅供内部使用）
func (s *SystemConfigServiceImpl) GetRawOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error) {
	ctx, span := s.tracer.Start(ctx, "SystemConfigService.GetRawOBSConfig")
	defer span.End()

	s.configLock.RLock()
	defer s.configLock.RUnlock()

	config, err := s.repo.SystemConfig().GetSystemConfig(ctx, "obs")
	if err != nil {
		return nil, fmt.Errorf("获取系统配置失败: %w", err)
	}

	obsConfig := config.GetOBSConfig()
	// 不进行掩码处理，返回原始值
	return obsConfig, nil
}

package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/casbin/casbin/v2"
	casbinmodel "github.com/casbin/casbin/v2/model"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// CasbinService Casbin权限检查服务 - 按照technical-design.md实现
type CasbinService struct {
	enforcer *casbin.SyncedEnforcer
	repo     repository.Repository
	logger   logger.LoggerService
	tracer   trace.Tracer
}

// NewCasbinService 创建Casbin服务实例
func NewCasbinService(db *gorm.DB, repo repository.Repository, loggerSvc logger.LoggerService, tracer trace.Tracer) (*CasbinService, error) {
	// 1. 创建数据库适配器
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, fmt.Errorf("failed to create casbin adapter: %w", err)
	}

	// 2. 获取RBAC模型配置
	rbacModel, err := repo.RBAC().GetActiveRBACModel(context.Background())
	if err != nil {
		// 如果没有激活的模型，使用默认模型
		rbacModel = model.DefaultRBACModel()
		loggerSvc.Info("使用默认RBAC模型")
	}

	// 3. 从模型内容创建Casbin模型
	modelText := rbacModel.Content
	m, err := casbinmodel.NewModelFromString(modelText)
	if err != nil {
		return nil, fmt.Errorf("failed to create casbin model: %w", err)
	}

	// 4. 从模型和适配器创建执行器
	enforcer, err := casbin.NewSyncedEnforcer(m, adapter)
	if err != nil {
		return nil, fmt.Errorf("failed to create casbin enforcer: %w", err)
	}

	// 5. 添加自定义函数 - 按照technical-design.md 3.3.1节要求
	enforcer.AddFunction("resourcePathMatch", ResourcePathMatchFunc)
	enforcer.AddFunction("actionMatch", ActionMatchFunc)

	// 7. 添加自定义匹配函数
	enforcer.AddFunction("resourcePathMatch", ResourcePathMatchFunc)
	enforcer.AddFunction("actionMatch", ActionMatchFunc)

	// 8. 配置执行器（新版本casbin已移除SetAutoSave和SetAutoNotifyEnabled方法）

	// 9. 加载策略
	err = enforcer.LoadPolicy()
	if err != nil {
		return nil, fmt.Errorf("failed to load casbin policy: %w", err)
	}

	service := &CasbinService{
		enforcer: enforcer,
		repo:     repo,
		logger:   loggerSvc,
		tracer:   tracer,
	}

	// 10. 初始化默认策略
	err = service.initDefaultPolicies(context.Background())
	if err != nil {
		logger.Warn("初始化默认策略失败", zap.Error(err))
	}

	return service, nil
}

// CheckUserPermission 检查用户权限 - 按照technical-design.md 3.3.4节权限检查流程
func (s *CasbinService) CheckUserPermission(userID uint, resourceType, resourcePath, action string) (bool, error) {
	// 按照technical-design.md 3.3.1节，主体格式为 user:123
	subject := fmt.Sprintf("user:%d", userID)

	s.logger.Debug("=== 开始Casbin权限检查 ===",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("resource_type", resourceType),
		zap.String("resource_path", resourcePath),
		zap.String("action", action))

	// 在权限检查前输出当前的RBAC模型配置
	s.logRBACModelInfo()

	// 在权限检查前输出相关的策略数据
	s.logRelevantPolicies(subject, resourceType, resourcePath, action)

	// 输出matcher执行前的参数信息
	s.logMatcherParameters(subject, resourceType, resourcePath, action)

	// 按照technical-design.md 3.3.1节的四元组权限检查模式
	allowed, err := s.enforcer.Enforce(subject, resourceType, resourcePath, action)
	if err != nil {
		s.logger.Error("=== Casbin权限检查失败 ===",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.String("resource_type", resourceType),
			zap.String("resource_path", resourcePath),
			zap.String("action", action),
			zap.Error(err))

		// 输出详细的调试信息
		s.logCasbinDebugInfo(subject)
		return false, err
	}

	s.logger.Debug("=== Casbin权限检查结果 ===",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("resource_type", resourceType),
		zap.String("resource_path", resourcePath),
		zap.String("action", action),
		zap.Bool("allowed", allowed))

	// 如果权限检查失败，也输出调试信息
	if !allowed {
		s.logger.Warn("权限检查失败，输出详细调试信息")
		s.logCasbinDebugInfo(subject)
	}

	return allowed, nil
}

// AddUserToGroup 添加用户到用户组 - 按照technical-design.md 3.3.1节主体格式
func (s *CasbinService) AddUserToGroup(userID uint, groupName string) error {
	subject := fmt.Sprintf("user:%d", userID)
	group := fmt.Sprintf("group:%s", groupName)
	_, err := s.enforcer.AddGroupingPolicy(subject, group)
	if err != nil {
		s.logger.Error("添加用户组关系失败",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.String("group", group),
			zap.Error(err))
		return err
	}

	s.logger.Debug("用户组关系添加成功",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("group", group))

	return nil
}

// RemoveUserFromGroup 从用户组移除用户 - 按照technical-design.md 3.3.1节主体格式
func (s *CasbinService) RemoveUserFromGroup(userID uint, groupName string) error {
	subject := fmt.Sprintf("user:%d", userID)
	_, err := s.enforcer.RemoveGroupingPolicy(subject, groupName)
	if err != nil {
		s.logger.Error("移除用户组关系失败",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.String("group", groupName),
			zap.Error(err))
		return err
	}

	s.logger.Debug("用户组关系移除成功",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("group", groupName))

	return nil
}

// AddUserPolicy 添加用户直接权限策略
func (s *CasbinService) AddUserPolicy(userID uint, resourceType, resourcePath, action string) error {
	subject := fmt.Sprintf("user:%d", userID)
	_, err := s.enforcer.AddPolicy(subject, resourceType, resourcePath, action)
	return err
}

// RemoveUserPolicy 移除用户直接权限策略
func (s *CasbinService) RemoveUserPolicy(userID uint, resourceType, resourcePath, action string) error {
	subject := fmt.Sprintf("user:%d", userID)
	_, err := s.enforcer.RemovePolicy(subject, resourceType, resourcePath, action)
	return err
}

// AddGroupPolicy 添加用户组权限策略 - 使用统一的p策略和group:前缀
func (s *CasbinService) AddGroupPolicy(groupName, resourceType, resourcePath, action string) error {
	subject := fmt.Sprintf("group:%s", groupName)
	_, err := s.enforcer.AddPolicy(subject, resourceType, resourcePath, action)
	return err
}

// RemoveGroupPolicy 移除用户组权限策略 - 使用统一的p策略和group:前缀
func (s *CasbinService) RemoveGroupPolicy(groupName, resourceType, resourcePath, action string) error {
	subject := fmt.Sprintf("group:%s", groupName)
	_, err := s.enforcer.RemovePolicy(subject, resourceType, resourcePath, action)
	return err
}

// GetUserGroups 获取用户所属的用户组
func (s *CasbinService) GetUserGroups(userID uint) ([]string, error) {
	subject := fmt.Sprintf("user:%d", userID)
	groups, err := s.enforcer.GetRolesForUser(subject)
	if err != nil {
		return nil, err
	}
	return groups, nil
}

// GetGroupMembers 获取用户组成员
func (s *CasbinService) GetGroupMembers(groupName string) ([]string, error) {
	users, err := s.enforcer.GetUsersForRole(groupName)
	if err != nil {
		return nil, err
	}
	return users, nil
}

// ExportPolicies 导出所有策略
func (s *CasbinService) ExportPolicies() ([][]string, error) {
	policies, err := s.enforcer.GetPolicy()
	if err != nil {
		return nil, err
	}
	groupings, err := s.enforcer.GetGroupingPolicy()
	if err != nil {
		return nil, err
	}

	var allPolicies [][]string

	// 添加策略规则
	for _, policy := range policies {
		rule := append([]string{"p"}, policy...)
		allPolicies = append(allPolicies, rule)
	}

	// 添加用户组权限规则
	p2Policies, err := s.enforcer.GetNamedPolicy("p2")
	if err != nil {
		return nil, err
	}
	for _, policy := range p2Policies {
		rule := append([]string{"p2"}, policy...)
		allPolicies = append(allPolicies, rule)
	}

	// 添加角色分配规则
	for _, grouping := range groupings {
		rule := append([]string{"g"}, grouping...)
		allPolicies = append(allPolicies, rule)
	}

	return allPolicies, nil
}

// GetEnforcer 获取Casbin执行器
func (s *CasbinService) GetEnforcer() *casbin.SyncedEnforcer {
	return s.enforcer
}

// initDefaultPolicies 初始化默认策略 - 按照technical-design.md 3.3.3节权限策略示例
func (s *CasbinService) initDefaultPolicies(ctx context.Context) error {
	// 检查是否已有策略
	policies, err := s.enforcer.GetPolicy()
	if err != nil {
		return fmt.Errorf("failed to get policies: %w", err)
	}
	if len(policies) > 0 {
		s.logger.Info("Casbin策略已存在，跳过初始化")
		return nil
	}

	s.logger.Info("初始化默认Casbin策略...")

	// 1. 添加admin用户组权限策略 (p) - 使用统一的p策略和group:前缀
	adminGroupPolicies := [][]string{
		{"group:admin", "*", "*", "*"}, // admin用户组拥有所有资源的完全权限
	}

	for _, policy := range adminGroupPolicies {
		params := make([]interface{}, len(policy))
		for i, v := range policy {
			params[i] = v
		}
		_, err := s.enforcer.AddPolicy(params...)
		if err != nil {
			s.logger.Error("添加admin用户组策略失败", zap.Error(err))
		} else {
			s.logger.Info("添加admin用户组策略成功", zap.Strings("policy", policy))
		}
	}

	// 2. 添加用户组成员关系 (g) - 按照technical-design.md 3.3.1节主体格式 user:1，使用group:前缀
	groupMemberships := [][]string{
		{"user:1", "group:admin"}, // admin用户(ID=1)属于admin用户组
	}

	for _, membership := range groupMemberships {
		params := make([]interface{}, len(membership))
		for i, v := range membership {
			params[i] = v
		}
		_, err := s.enforcer.AddNamedGroupingPolicy("g", params...)
		if err != nil {
			s.logger.Error("添加用户组关系失败", zap.Error(err))
		} else {
			s.logger.Info("添加用户组关系成功", zap.Strings("membership", membership))
		}
	}

	// 保存策略
	err = s.enforcer.SavePolicy()
	if err != nil {
		return fmt.Errorf("保存默认策略失败: %w", err)
	}

	s.logger.Info("默认Casbin策略初始化成功")
	return nil
}

// logCasbinDebugInfo 输出Casbin调试信息
func (s *CasbinService) logCasbinDebugInfo(subject string) {
	s.logger.Debug("=== Casbin调试信息 ===")

	// 输出用户直接权限策略
	userPolicies, err := s.enforcer.GetFilteredPolicy(0, subject)
	if err != nil {
		s.logger.Error("获取用户直接权限策略失败", zap.Error(err))
	} else {
		s.logger.Debug("用户直接权限策略",
			zap.String("subject", subject),
			zap.Any("policies", userPolicies))
	}

	// 输出用户组成员关系
	userGroups, err := s.enforcer.GetRolesForUser(subject)
	if err != nil {
		s.logger.Error("获取用户组成员关系失败", zap.Error(err))
	} else {
		s.logger.Debug("用户组成员关系",
			zap.String("subject", subject),
			zap.Strings("groups", userGroups))

		// 输出用户组权限策略 - 使用统一的p策略
		for _, group := range userGroups {
			groupPolicies, err := s.enforcer.GetFilteredPolicy(0, group)
			if err != nil {
				s.logger.Error("获取用户组权限策略失败", zap.String("group", group), zap.Error(err))
			} else {
				s.logger.Debug("用户组权限策略",
					zap.String("group", group),
					zap.Any("policies", groupPolicies))
			}
		}
	}

	// 输出所有策略
	allPolicies, err := s.enforcer.GetPolicy()
	if err != nil {
		s.logger.Error("获取所有用户策略失败", zap.Error(err))
	}

	// 不再需要获取p2策略，因为已经统一使用p策略

	allGroupings, err := s.enforcer.GetGroupingPolicy()
	if err != nil {
		s.logger.Error("获取所有用户组成员关系失败", zap.Error(err))
	}

	s.logger.Debug("所有权限策略",
		zap.Any("all_policies", allPolicies),
		zap.Any("group_memberships", allGroupings))

	s.logger.Debug("=== Casbin调试信息结束 ===")
}

// logRBACModelInfo 输出当前RBAC模型配置信息
func (s *CasbinService) logRBACModelInfo() {
	model := s.enforcer.GetModel()
	if model == nil {
		s.logger.Error("Casbin模型为空")
		return
	}

	s.logger.Debug("=== RBAC模型配置信息 ===")

	// 输出请求定义
	if reqDef := model["r"]; reqDef != nil {
		for key, assertion := range reqDef {
			s.logger.Debug("请求定义", zap.String("key", key), zap.String("value", assertion.Value))
		}
	}

	// 输出策略定义
	if polDef := model["p"]; polDef != nil {
		for key, assertion := range polDef {
			s.logger.Debug("策略定义", zap.String("key", key), zap.String("value", assertion.Value))
		}
	}

	// 输出角色定义
	if roleDef := model["g"]; roleDef != nil {
		for key, assertion := range roleDef {
			s.logger.Debug("角色定义", zap.String("key", key), zap.String("value", assertion.Value))
		}
	}

	// 输出匹配器
	if matcher := model["m"]; matcher != nil {
		for key, assertion := range matcher {
			s.logger.Debug("匹配器", zap.String("key", key), zap.String("value", assertion.Value))
		}
	}

	s.logger.Debug("=== RBAC模型配置信息结束 ===")
}

// logRelevantPolicies 输出与当前权限检查相关的策略
func (s *CasbinService) logRelevantPolicies(subject, resourceType, resourcePath, action string) {
	s.logger.Debug("=== 相关权限策略信息 ===")

	// 输出用户直接权限策略
	userPolicies, err := s.enforcer.GetFilteredPolicy(0, subject)
	if err != nil {
		s.logger.Error("获取用户直接权限策略失败", zap.Error(err))
	} else {
		s.logger.Debug("用户直接权限策略",
			zap.String("subject", subject),
			zap.Int("count", len(userPolicies)),
			zap.Any("policies", userPolicies))
	}

	// 输出用户组成员关系
	userGroups, err := s.enforcer.GetRolesForUser(subject)
	if err != nil {
		s.logger.Error("获取用户组成员关系失败", zap.Error(err))
	} else {
		s.logger.Debug("用户组成员关系",
			zap.String("subject", subject),
			zap.Strings("groups", userGroups))

		// 输出每个用户组的权限策略
		for _, group := range userGroups {
			// 移除group:前缀，因为userGroups已经包含了完整的组名
			groupSubject := group
			if !strings.HasPrefix(group, "group:") {
				groupSubject = fmt.Sprintf("group:%s", group)
			}
			groupPolicies, err := s.enforcer.GetFilteredPolicy(0, groupSubject)
			if err != nil {
				s.logger.Error("获取用户组权限策略失败", zap.String("group", group), zap.Error(err))
			} else {
				s.logger.Debug("用户组权限策略",
					zap.String("group", group),
					zap.String("group_subject", groupSubject),
					zap.Int("count", len(groupPolicies)),
					zap.Any("policies", groupPolicies))

				// 检查是否有匹配的策略
				for _, policy := range groupPolicies {
					if len(policy) >= 4 {
						policySubject := policy[0]
						policyResourceType := policy[1]
						policyResourcePath := policy[2]
						policyAction := policy[3]

						s.logger.Debug("策略匹配检查",
							zap.String("group", group),
							zap.String("policy_subject", policySubject),
							zap.String("policy_resource_type", policyResourceType),
							zap.String("policy_resource_path", policyResourcePath),
							zap.String("policy_action", policyAction),
							zap.String("request_resource_type", resourceType),
							zap.String("request_resource_path", resourcePath),
							zap.String("request_action", action))
					}
				}
			}
		}
	}

	s.logger.Debug("=== 相关权限策略信息结束 ===")
}

// logMatcherParameters 输出matcher执行前的参数信息
func (s *CasbinService) logMatcherParameters(subject, resourceType, resourcePath, action string) {
	s.logger.Debug("=== matcher执行前的参数信息 ===")

	// 输出请求参数
	s.logger.Debug("请求参数",
		zap.String("subject", subject),
		zap.String("resource_type", resourceType),
		zap.String("resource_path", resourcePath),
		zap.String("action", action))

	// 获取并输出用户组成员关系
	userGroups, err := s.enforcer.GetRolesForUser(subject)
	if err != nil {
		s.logger.Error("获取用户组成员关系失败", zap.Error(err))
	} else {
		s.logger.Debug("用户组成员关系",
			zap.String("subject", subject),
			zap.Strings("groups", userGroups))
	}

	// 获取并输出用户直接权限策略
	userPolicies, err := s.enforcer.GetFilteredPolicy(0, subject)
	if err != nil {
		s.logger.Error("获取用户直接权限策略失败", zap.Error(err))
	} else {
		s.logger.Debug("用户直接权限策略",
			zap.String("subject", subject),
			zap.Any("policies", userPolicies))
	}

	// 获取并输出用户组权限策略 - 使用统一的p策略
	for _, group := range userGroups {
		groupPolicies, err := s.enforcer.GetFilteredPolicy(0, group)
		if err != nil {
			s.logger.Error("获取用户组权限策略失败", zap.String("group", group), zap.Error(err))
		} else {
			s.logger.Debug("用户组权限策略",
				zap.String("group", group),
				zap.Any("policies", groupPolicies))
		}
	}

	s.logger.Debug("=== matcher执行前的参数信息结束 ===")
}

// ResourcePathMatchFunc 资源路径匹配函数
func ResourcePathMatchFunc(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return false, nil
	}

	requestPath := args[0].(string)
	policyPath := args[1].(string)

	// 如果策略路径是通配符，直接匹配
	if policyPath == "*" {
		return true, nil
	}

	// 精确匹配
	if requestPath == policyPath {
		return true, nil
	}

	// 通配符匹配
	// 将策略路径转换为正则表达式
	pattern := strings.ReplaceAll(policyPath, "*", ".*")
	pattern = "^" + pattern + "$"

	matched, err := regexp.MatchString(pattern, requestPath)
	if err != nil {
		return false, err
	}

	return matched, nil
}

// ActionMatchFunc 动作匹配函数
func ActionMatchFunc(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return false, nil
	}

	requestAction := args[0].(string)
	policyAction := args[1].(string)

	// 如果策略动作是通配符，直接匹配
	if policyAction == "*" {
		return true, nil
	}

	// 精确匹配
	if requestAction == policyAction {
		return true, nil
	}

	// 多个动作用逗号分隔
	actions := strings.Split(policyAction, ",")
	for _, action := range actions {
		if strings.TrimSpace(action) == requestAction {
			return true, nil
		}
	}

	return false, nil
}

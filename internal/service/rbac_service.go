package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/casbin/casbin/v2"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"
)

// rbacService RBAC服务实现 - 按照technical-design.md 3.1-3.5节设计
type rbacService struct {
	casbinService  *CasbinService
	k8sRBACService k8s.RBACService
	repo           repository.Repository
	logger         logger.LoggerService
	tracer         trace.Tracer
	initialized    bool // 权限初始化状态标记，防止重复初始化
}

// NewRBACService 创建RBAC服务实例
func NewRBACService(
	casbinService *CasbinService,
	k8sRBACService k8s.RBACService,
	repo repository.Repository,
	loggerSvc logger.LoggerService,
	tracer trace.Tracer,
) RBACService {
	return &rbacService{
		casbinService:  casbinService,
		k8sRBACService: k8sRBACService,
		repo:           repo,
		logger:         loggerSvc,
		tracer:         tracer,
	}
}

// CheckPermission 检查用户权限 - 按照technical-design.md 3.3.4节权限检查流程
func (s *rbacService) CheckPermission(ctx context.Context, userID uint, resourcePath string, action string) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.CheckPermission")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.String("permission.resource_path", resourcePath),
		attribute.String("permission.action", action),
	)

	// 解析资源路径获取资源类型 - 按照technical-design.md 3.1.1节权限路径格式
	resourceType, scopePath, err := s.parseResourcePath(resourcePath)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("解析资源路径失败",
			zap.Uint("user_id", userID),
			zap.String("resource_path", resourcePath),
			zap.Error(err))
		return false, err
	}

	subject := fmt.Sprintf("user:%d", userID)

	s.logger.Debug("开始权限检查",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("resource_type", resourceType),
		zap.String("resource_path", scopePath),
		zap.String("action", action))

	// 使用Casbin进行权限检查 - 按照technical-design.md 3.3.1节四元组权限检查模式
	allowed, err := s.casbinService.CheckUserPermission(userID, resourceType, scopePath, action)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("权限检查失败",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.String("resource_type", resourceType),
			zap.String("resource_path", scopePath),
			zap.String("action", action),
			zap.Error(err))

		// 在权限检查失败时，输出当前用户的所有权限策略
		s.logUserPolicies(ctx, userID)
		return false, err
	}

	s.logger.Debug("权限检查完成",
		zap.Uint("user_id", userID),
		zap.String("subject", subject),
		zap.String("resource_type", resourceType),
		zap.String("resource_path", scopePath),
		zap.String("action", action),
		zap.Bool("allowed", allowed))

	span.SetAttributes(attribute.Bool("permission.allowed", allowed))
	return allowed, nil
}

// BatchCheckPermission 批量检查权限
func (s *rbacService) BatchCheckPermission(ctx context.Context, userID uint, permissions []string) (map[string]bool, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.BatchCheckPermission")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int("permissions.count", len(permissions)),
	)

	results := make(map[string]bool)
	for _, perm := range permissions {
		// 解析权限格式：resource_path:action
		permParts := strings.Split(perm, ":")
		if len(permParts) < 2 {
			s.logger.Warn("权限格式无效", zap.String("permission", perm))
			results[perm] = false
			continue
		}

		resourcePath := strings.Join(permParts[:len(permParts)-1], ":")
		action := permParts[len(permParts)-1]

		allowed, err := s.CheckPermission(ctx, userID, resourcePath, action)
		if err != nil {
			s.logger.Error("权限检查失败", zap.String("permission", perm), zap.Error(err))
			results[perm] = false
			continue
		}
		results[perm] = allowed
	}

	return results, nil
}

// optimizeGroupPermissions 优化用户组权限策略，避免冗余
func (s *rbacService) optimizeGroupPermissions(permissions []*model.ResourcePermission) []*model.ResourcePermission {
	// 检查是否有通配符权限
	hasWildcard := false
	var wildcardPerm *model.ResourcePermission

	for _, perm := range permissions {
		if perm.ResourcePath == "system:*" && perm.Actions == "*" {
			hasWildcard = true
			wildcardPerm = perm
			break
		}
	}

	// 如果有通配符权限，只返回通配符权限
	if hasWildcard {
		s.logger.Debug("发现通配符权限，优化权限策略",
			zap.String("wildcard_path", wildcardPerm.ResourcePath),
			zap.Int("original_count", len(permissions)))
		return []*model.ResourcePermission{wildcardPerm}
	}

	// 否则返回所有权限
	return permissions
}

// CheckPermissionWithAttrs 带属性的权限检查
func (s *rbacService) CheckPermissionWithAttrs(ctx context.Context, userID uint, resourcePath string, action string, attrs map[string]interface{}) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.CheckPermissionWithAttrs")
	defer span.End()

	// 基础权限检查
	allowed, err := s.CheckPermission(ctx, userID, resourcePath, action)
	if err != nil || !allowed {
		return allowed, err
	}

	// TODO: 实现属性级权限检查（如资源所有者检查等）
	// 这里可以根据attrs中的属性进行更细粒度的权限控制

	return allowed, nil
}

// CheckHierarchicalPermission 层次化权限检查 - 按照technical-design.md 3.4.6节权限继承
func (s *rbacService) CheckHierarchicalPermission(ctx context.Context, userID uint, resourcePath string, action string, scopeLevel string) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.CheckHierarchicalPermission")
	defer span.End()

	// 按照优先级从高到低检查权限：应用级→项目级→集群级→系统级
	hierarchyPaths := s.buildHierarchyPaths(resourcePath, scopeLevel)

	for i, path := range hierarchyPaths {
		allowed, err := s.CheckPermission(ctx, userID, path, action)
		if err != nil {
			s.logger.Error("层次化权限检查失败",
				zap.Int("level", i),
				zap.String("path", path),
				zap.Error(err))
			continue
		}
		if allowed {
			span.SetAttributes(
				attribute.Bool("permission.allowed", true),
				attribute.Int("permission.matched_level", i),
				attribute.String("permission.matched_path", path),
			)
			return true, nil
		}
	}

	span.SetAttributes(attribute.Bool("permission.allowed", false))
	return false, nil
}

// parseResourcePath 解析资源路径 - 按照technical-design.md 3.1.1节权限路径格式
func (s *rbacService) parseResourcePath(resourcePath string) (resourceType, scopePath string, err error) {
	// 兼容旧格式：如果没有冒号，默认为api类型的system级权限
	if !strings.Contains(resourcePath, ":") {
		return "api", fmt.Sprintf("system:%s", resourcePath), nil
	}

	// 权限路径格式：{resource_type}:{scope_path}
	parts := strings.SplitN(resourcePath, ":", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("无效的资源路径格式: %s, 期望格式: {resource_type}:{scope_path}", resourcePath)
	}

	resourceType = parts[0]
	scopePath = parts[1]

	// 验证资源类型 - 按照technical-design.md 3.1.1节资源类型定义
	validTypes := map[string]bool{
		"api":    true, // 平台API权限
		"ui":     true, // 界面访问权限
		"k8s":    true, // Kubernetes资源权限
		"system": true, // 兼容旧格式，system作为scope_path的一部分
	}

	// 如果第一部分是system，说明是旧格式，需要重新解析
	if resourceType == "system" {
		return "api", resourcePath, nil
	}

	if !validTypes[resourceType] {
		return "", "", fmt.Errorf("无效的资源类型: %s, 支持的类型: api, ui, k8s", resourceType)
	}

	return resourceType, scopePath, nil
}

// buildHierarchyPaths 构建层次化权限路径 - 按照technical-design.md 3.4.6节权限继承
func (s *rbacService) buildHierarchyPaths(resourcePath, scopeLevel string) []string {
	resourceType, scopePath, err := s.parseResourcePath(resourcePath)
	if err != nil {
		s.logger.Error("解析资源路径失败", zap.String("resource_path", resourcePath), zap.Error(err))
		return []string{resourcePath}
	}

	var paths []string

	// 根据当前路径构建层次化路径
	switch {
	case strings.Contains(scopePath, "application:"):
		// 应用级权限 -> 项目级权限 -> 集群级权限 -> 系统级权限
		paths = append(paths, resourcePath) // 原始应用级权限

		// 构建项目级权限
		if idx := strings.Index(scopePath, ":application:"); idx > 0 {
			projectPath := resourceType + ":" + scopePath[:idx]
			paths = append(paths, projectPath)
		}

		// 构建集群级权限
		if strings.HasPrefix(scopePath, "cluster:") && strings.Contains(scopePath, ":project:") {
			if idx := strings.Index(scopePath, ":project:"); idx > 0 {
				clusterPath := resourceType + ":" + scopePath[:idx]
				paths = append(paths, clusterPath)
			}
		}

	case strings.Contains(scopePath, "project:"):
		// 项目级权限 -> 集群级权限 -> 系统级权限
		paths = append(paths, resourcePath) // 原始项目级权限

		// 构建集群级权限
		if strings.HasPrefix(scopePath, "cluster:") && strings.Contains(scopePath, ":project:") {
			if idx := strings.Index(scopePath, ":project:"); idx > 0 {
				clusterPath := resourceType + ":" + scopePath[:idx]
				paths = append(paths, clusterPath)
			}
		}

	case strings.HasPrefix(scopePath, "cluster:"):
		// 集群级权限 -> 系统级权限
		paths = append(paths, resourcePath) // 原始集群级权限

	default:
		// 系统级权限
		paths = append(paths, resourcePath)
	}

	// 最后添加系统级通配符权限
	systemPath := resourceType + ":system:*"
	paths = append(paths, systemPath)

	return paths
}

// GetUserPermissions 获取用户权限列表
func (s *rbacService) GetUserPermissions(ctx context.Context, userID uint) ([]string, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetUserPermissions")
	defer span.End()

	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// 获取用户直接权限
	userPermissions, err := s.repo.RBAC().GetUserPermissions(ctx, userID)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("获取用户直接权限失败", zap.Uint("user_id", userID), zap.Error(err))
	}

	// 获取用户组权限
	userGroups, err := s.repo.UserGroup().GetUserGroups(ctx, userID)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("获取用户组失败", zap.Uint("user_id", userID), zap.Error(err))
	}

	// 合并权限
	permissionSet := make(map[string]bool)

	// 添加用户直接权限
	for _, perm := range userPermissions {
		permKey := fmt.Sprintf("%s:%s:%s", perm.ResourceType, perm.ResourcePath, perm.Actions)
		permissionSet[permKey] = true
	}

	// 添加用户组权限
	for _, group := range userGroups {
		groupPermissions, err := s.repo.RBAC().GetGroupPermissions(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组权限失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		for _, perm := range groupPermissions {
			permKey := fmt.Sprintf("%s:%s:%s", perm.ResourceType, perm.ResourcePath, perm.Actions)
			permissionSet[permKey] = true
		}
	}

	// 转换为切片
	permissions := make([]string, 0, len(permissionSet))
	for perm := range permissionSet {
		permissions = append(permissions, perm)
	}

	span.SetAttributes(attribute.Int("permissions.count", len(permissions)))
	return permissions, nil
}

// CreatePermission 创建权限资源
func (s *rbacService) CreatePermission(ctx context.Context, req *model.PermissionCreateRequest) (*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.CreatePermission")
	defer span.End()

	span.SetAttributes(
		attribute.String("permission.name", req.Name),
		attribute.String("permission.resource_type", req.ResourceType),
		attribute.String("permission.resource_path", req.ResourcePath),
	)

	// 验证权限路径格式
	_, _, err := s.parseResourcePath(req.ResourceType + ":" + req.ResourcePath)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("权限路径格式无效: %w", err)
	}

	// 创建权限对象
	permission := &model.ResourcePermission{
		Name:         req.Name,
		ResourceType: req.ResourceType,
		ResourcePath: req.ResourcePath,
		Actions:      req.Actions,
		ScopeLevel:   req.ScopeLevel,
		Description:  req.Description,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存到数据库
	err = s.repo.RBAC().CreatePermission(ctx, permission)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("创建权限失败: %w", err)
	}

	s.logger.Info("权限创建成功",
		zap.Uint("permission_id", permission.ID),
		zap.String("name", permission.Name),
		zap.String("resource_type", permission.ResourceType),
		zap.String("resource_path", permission.ResourcePath))

	return permission, nil
}

// AssignPermissionToUser 为用户分配权限 - 按照technical-design.md 3.2节权限分配流程
func (s *rbacService) AssignPermissionToUser(ctx context.Context, userID, permissionID uint, grantedBy uint, reason string) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.AssignPermissionToUser")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("permission.id", int64(permissionID)),
		attribute.Int64("granted_by", int64(grantedBy)),
	)

	// 获取权限信息
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, permissionID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取权限信息失败: %w", err)
	}

	// 创建用户权限关联
	userPermission := &model.UserResourcePermission{
		UserID:               userID,
		ResourcePermissionID: permissionID,
		GrantedBy:            grantedBy,
		GrantReason:          reason,
		CreatedAt:            time.Now(),
	}

	err = s.repo.RBAC().GrantUserPermission(ctx, userPermission)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("分配权限失败: %w", err)
	}

	// 更新Casbin策略
	err = s.casbinService.AddUserPolicy(userID, permission.ResourceType, permission.ResourcePath, permission.Actions)
	if err != nil {
		s.logger.Error("更新Casbin策略失败", zap.Error(err))
		// 不返回错误，因为数据库已经更新，可以通过策略同步修复
	}

	// 如果是K8s权限，同步到K8s集群
	if permission.ResourceType == "k8s" {
		err = s.syncK8sPermission(ctx, "user", userID, permission)
		if err != nil {
			s.logger.Error("同步K8s权限失败", zap.Error(err))
			// 不返回错误，权限已分配，K8s同步可以异步重试
		}
	}

	s.logger.Info("用户权限分配成功",
		zap.Uint("user_id", userID),
		zap.Uint("permission_id", permissionID),
		zap.String("resource_type", permission.ResourceType),
		zap.String("resource_path", permission.ResourcePath))

	return nil
}

// AssignPermissionToGroup 为用户组分配权限 - 按照technical-design.md 3.2节权限分配流程
func (s *rbacService) AssignPermissionToGroup(ctx context.Context, groupID, permissionID uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.AssignPermissionToGroup")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.Int64("permission.id", int64(permissionID)),
	)

	// 获取用户组信息
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, groupID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取用户组信息失败: %w", err)
	}

	// 获取权限信息
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, permissionID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取权限信息失败: %w", err)
	}

	// 创建用户组权限关联
	err = s.repo.RBAC().AssignGroupPermissions(ctx, groupID, []uint{permissionID})
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("分配权限失败: %w", err)
	}

	// 更新Casbin策略
	err = s.casbinService.AddGroupPolicy(group.Name, permission.ResourceType, permission.ResourcePath, permission.Actions)
	if err != nil {
		s.logger.Error("更新Casbin策略失败", zap.Error(err))
		// 不返回错误，因为数据库已经更新，可以通过策略同步修复
	}

	// 如果是K8s权限，同步到K8s集群
	if permission.ResourceType == "k8s" {
		err = s.syncK8sPermission(ctx, "group", groupID, permission)
		if err != nil {
			s.logger.Error("同步K8s权限失败", zap.Error(err))
			// 不返回错误，权限已分配，K8s同步可以异步重试
		}
	}

	s.logger.Info("用户组权限分配成功",
		zap.Uint("group_id", groupID),
		zap.String("group_name", group.Name),
		zap.Uint("permission_id", permissionID),
		zap.String("resource_type", permission.ResourceType),
		zap.String("resource_path", permission.ResourcePath))

	return nil
}

// RevokePermissionFromUser 撤销用户权限
func (s *rbacService) RevokePermissionFromUser(ctx context.Context, userID, permissionID uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RevokePermissionFromUser")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("permission.id", int64(permissionID)),
	)

	// 获取权限信息
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, permissionID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取权限信息失败: %w", err)
	}

	// 撤销权限关联
	err = s.repo.RBAC().RevokeUserPermission(ctx, userID, permissionID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("撤销权限失败: %w", err)
	}

	// 更新Casbin策略
	err = s.casbinService.RemoveUserPolicy(userID, permission.ResourceType, permission.ResourcePath, permission.Actions)
	if err != nil {
		s.logger.Error("更新Casbin策略失败", zap.Error(err))
	}

	s.logger.Info("用户权限撤销成功",
		zap.Uint("user_id", userID),
		zap.Uint("permission_id", permissionID),
		zap.String("resource_type", permission.ResourceType),
		zap.String("resource_path", permission.ResourcePath))

	return nil
}

// RevokePermissionFromGroup 撤销用户组权限
func (s *rbacService) RevokePermissionFromGroup(ctx context.Context, groupID, permissionID uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RevokePermissionFromGroup")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.Int64("permission.id", int64(permissionID)),
	)

	// 获取用户组信息
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, groupID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取用户组信息失败: %w", err)
	}

	// 获取权限信息
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, permissionID)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取权限信息失败: %w", err)
	}

	// 撤销权限关联
	err = s.repo.RBAC().RemoveGroupPermissions(ctx, groupID, []uint{permissionID})
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("撤销权限失败: %w", err)
	}

	// 更新Casbin策略
	err = s.casbinService.RemoveGroupPolicy(group.Name, permission.ResourceType, permission.ResourcePath, permission.Actions)
	if err != nil {
		s.logger.Error("更新Casbin策略失败", zap.Error(err))
	}

	s.logger.Info("用户组权限撤销成功",
		zap.Uint("group_id", groupID),
		zap.String("group_name", group.Name),
		zap.Uint("permission_id", permissionID),
		zap.String("resource_type", permission.ResourceType),
		zap.String("resource_path", permission.ResourcePath))

	return nil
}

// syncK8sPermission 同步K8s权限 - 按照technical-design.md 3.4节K8s权限同步实现
func (s *rbacService) syncK8sPermission(ctx context.Context, subjectType string, subjectID uint, permission *model.ResourcePermission) error {
	if s.k8sRBACService == nil {
		s.logger.Warn("K8s RBAC服务未初始化，跳过K8s权限同步")
		return nil
	}

	// 解析权限路径获取集群信息
	if !strings.HasPrefix(permission.ResourcePath, "cluster:") {
		// 非集群相关权限，无需同步到K8s
		return nil
	}

	var subjectName string
	var err error

	if subjectType == "user" {
		user, err := s.repo.User().GetUserByID(ctx, subjectID)
		if err != nil {
			return fmt.Errorf("获取用户信息失败: %w", err)
		}
		subjectName = user.Username
	} else if subjectType == "group" {
		group, err := s.repo.UserGroup().GetUserGroupByID(ctx, subjectID)
		if err != nil {
			return fmt.Errorf("获取用户组信息失败: %w", err)
		}
		subjectName = group.Name
	} else {
		return fmt.Errorf("不支持的主体类型: %s", subjectType)
	}

	// 解析集群名称（简化实现）
	pathParts := strings.Split(permission.ResourcePath, ":")
	if len(pathParts) < 2 {
		return fmt.Errorf("无效的权限路径格式: %s", permission.ResourcePath)
	}
	clusterName := pathParts[1]

	// 解析资源和动作
	resources := []string{"pods", "deployments", "services"} // 简化实现，实际应该从权限路径解析
	verbs := strings.Split(permission.Actions, ",")

	// 执行K8s权限同步
	if subjectType == "user" {
		err = s.k8sRBACService.CreateUserK8sPermission(ctx, clusterName, subjectName, resources, verbs)
	} else {
		// 对于用户组，暂时跳过K8s同步
		s.logger.Info("用户组K8s权限同步暂未实现", zap.String("group_name", subjectName))
		return nil
	}

	if err != nil {
		return fmt.Errorf("同步K8s权限失败: %w", err)
	}

	s.logger.Info("K8s权限同步成功",
		zap.String("subject_type", subjectType),
		zap.String("subject_name", subjectName),
		zap.String("cluster_name", clusterName),
		zap.String("resource_path", permission.ResourcePath),
		zap.String("actions", permission.Actions))

	return nil
}

// SyncAllPermissions 同步所有权限策略 - 按照technical-design.md 3.4.5节权限策略同步
func (s *rbacService) SyncAllPermissions(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.SyncAllPermissions")
	defer span.End()

	s.logger.Info("开始同步所有权限策略...")

	// 1. 清空现有策略
	s.casbinService.enforcer.ClearPolicy()

	// 2. 同步用户组权限策略
	groups, _, err := s.repo.UserGroup().ListUserGroupsPaginated(ctx, 1, 1000)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取用户组列表失败: %w", err)
	}

	groupCount := 0
	for _, group := range groups {
		permissions, err := s.repo.RBAC().GetGroupPermissions(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组权限失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		// 优化权限策略：如果用户组有通配符权限，只保留通配符权限
		optimizedPermissions := s.optimizeGroupPermissions(permissions)

		for _, perm := range optimizedPermissions {
			err = s.casbinService.AddGroupPolicy(group.Name, perm.ResourceType, perm.ResourcePath, perm.Actions)
			if err != nil {
				s.logger.Error("添加用户组权限策略失败",
					zap.String("group", group.Name),
					zap.String("resource_path", perm.ResourcePath),
					zap.Error(err))
			}
		}
		groupCount++
	}

	// 3. 同步用户组成员关系
	for _, group := range groups {
		members, err := s.repo.UserGroup().GetGroupMembers(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组成员失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		for _, member := range members {
			err = s.casbinService.AddUserToGroup(member.ID, group.Name)
			if err != nil {
				s.logger.Error("添加用户组成员关系失败",
					zap.Uint("user_id", member.ID),
					zap.String("group_name", group.Name),
					zap.Error(err))
			}
		}
	}

	// 4. 同步用户直接权限策略
	// TODO: 实现用户权限同步，需要添加ListAllUserPermissions方法
	userCount := 0

	// 4. 保存策略
	err = s.casbinService.enforcer.SavePolicy()
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("保存权限策略失败: %w", err)
	}

	s.logger.Info("权限策略同步完成",
		zap.Int("groups_synced", groupCount),
		zap.Int("users_synced", userCount))

	span.SetAttributes(
		attribute.Int("groups.synced", groupCount),
		attribute.Int("users.synced", userCount),
	)

	return nil
}

// GetPermissionByID 根据ID获取权限
func (s *rbacService) GetPermissionByID(ctx context.Context, id uint) (*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetPermissionByID")
	defer span.End()

	span.SetAttributes(attribute.Int64("permission.id", int64(id)))

	return s.repo.RBAC().GetPermissionByID(ctx, id)
}

// GetPermissionByPath 根据路径获取权限
func (s *rbacService) GetPermissionByPath(ctx context.Context, resourcePath string) (*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetPermissionByPath")
	defer span.End()

	span.SetAttributes(attribute.String("permission.resource_path", resourcePath))

	return s.repo.RBAC().GetPermissionByPath(ctx, resourcePath)
}

// GetPermissionByPathAndActions 根据路径和操作获取权限
func (s *rbacService) GetPermissionByPathAndActions(ctx context.Context, resourcePath, actions string) (*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetPermissionByPathAndActions")
	defer span.End()

	span.SetAttributes(
		attribute.String("permission.resource_path", resourcePath),
		attribute.String("permission.actions", actions),
	)

	return s.repo.RBAC().GetPermissionByPathAndActions(ctx, resourcePath, actions)
}

// UpdatePermission 更新权限
func (s *rbacService) UpdatePermission(ctx context.Context, id uint, req *model.PermissionUpdateRequest) (*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.UpdatePermission")
	defer span.End()

	span.SetAttributes(attribute.Int64("permission.id", int64(id)))

	// 获取现有权限
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("获取权限失败: %w", err)
	}

	// 更新权限字段
	if req.Name != "" {
		permission.Name = req.Name
	}
	if req.Description != "" {
		permission.Description = req.Description
	}
	if req.Actions != "" {
		permission.Actions = req.Actions
	}
	permission.UpdatedAt = time.Now()

	// 保存更新
	err = s.repo.RBAC().UpdatePermission(ctx, permission)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("更新权限失败: %w", err)
	}

	s.logger.Info("权限更新成功",
		zap.Uint("permission_id", permission.ID),
		zap.String("name", permission.Name))

	return permission, nil
}

// DeletePermission 删除权限
func (s *rbacService) DeletePermission(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.DeletePermission")
	defer span.End()

	span.SetAttributes(attribute.Int64("permission.id", int64(id)))

	// 获取权限信息
	permission, err := s.repo.RBAC().GetPermissionByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("获取权限失败: %w", err)
	}

	// 删除权限
	err = s.repo.RBAC().DeletePermission(ctx, id)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("删除权限失败: %w", err)
	}

	s.logger.Info("权限删除成功",
		zap.Uint("permission_id", id),
		zap.String("name", permission.Name))

	return nil
}

// ListPermissions 获取权限列表
func (s *rbacService) ListPermissions(ctx context.Context, page, pageSize int) ([]*model.ResourcePermission, int64, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.ListPermissions")
	defer span.End()

	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("page_size", pageSize),
	)

	return s.repo.RBAC().ListPermissions(ctx, page, pageSize)
}

// ValidatePermissionPath 验证权限路径
func (s *rbacService) ValidatePermissionPath(resourcePath string) error {
	_, _, err := s.parseResourcePath(resourcePath)
	return err
}

// GetUserEffectivePermissions 获取用户有效权限（包括直接权限和用户组权限）
func (s *rbacService) GetUserEffectivePermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetUserEffectivePermissions")
	defer span.End()

	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// 获取用户直接权限
	userPermissions, err := s.repo.RBAC().GetUserPermissions(ctx, userID)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("获取用户直接权限失败: %w", err)
	}

	// 获取用户组权限
	userGroups, err := s.repo.UserGroup().GetUserGroups(ctx, userID)
	if err != nil {
		span.RecordError(err)
		s.logger.Error("获取用户组失败", zap.Uint("user_id", userID), zap.Error(err))
	}

	// 合并权限（使用map去重）
	permissionMap := make(map[uint]*model.ResourcePermission)

	// 添加用户直接权限
	for _, perm := range userPermissions {
		permissionMap[perm.ID] = perm
	}

	// 添加用户组权限
	for _, group := range userGroups {
		groupPermissions, err := s.repo.RBAC().GetGroupPermissions(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组权限失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		for _, perm := range groupPermissions {
			permissionMap[perm.ID] = perm
		}
	}

	// 转换为切片
	permissions := make([]*model.ResourcePermission, 0, len(permissionMap))
	for _, perm := range permissionMap {
		permissions = append(permissions, perm)
	}

	span.SetAttributes(attribute.Int("permissions.count", len(permissions)))
	return permissions, nil
}

// GrantUserPermission 授予用户权限
func (s *rbacService) GrantUserPermission(ctx context.Context, userID uint, req *model.UserPermissionRequest, grantedBy uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.GrantUserPermission")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("permission.id", int64(req.PermissionID)),
		attribute.Int64("granted_by", int64(grantedBy)),
	)

	return s.AssignPermissionToUser(ctx, userID, req.PermissionID, grantedBy, req.Reason)
}

// RevokeUserPermission 撤销用户权限
func (s *rbacService) RevokeUserPermission(ctx context.Context, userID, permissionID uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RevokeUserPermission")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("permission.id", int64(permissionID)),
	)

	return s.RevokePermissionFromUser(ctx, userID, permissionID)
}

// GetUserDirectPermissions 获取用户直接权限
func (s *rbacService) GetUserDirectPermissions(ctx context.Context, userID uint) ([]*model.UserResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetUserDirectPermissions")
	defer span.End()

	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	return s.repo.RBAC().GetUserDirectPermissions(ctx, userID)
}

// AssignGroupPermissions 为用户组分配权限
func (s *rbacService) AssignGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.AssignGroupPermissions")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	return s.repo.RBAC().AssignGroupPermissions(ctx, groupID, permissionIDs)
}

// RemoveGroupPermissions 移除用户组权限
func (s *rbacService) RemoveGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RemoveGroupPermissions")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	return s.repo.RBAC().RemoveGroupPermissions(ctx, groupID, permissionIDs)
}

// GetGroupPermissions 获取用户组权限
func (s *rbacService) GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetGroupPermissions")
	defer span.End()

	span.SetAttributes(attribute.Int64("group.id", int64(groupID)))

	return s.repo.RBAC().GetGroupPermissions(ctx, groupID)
}

// AssignUserPermissions 批量分配用户权限
func (s *rbacService) AssignUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.AssignUserPermissions")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	return s.repo.RBAC().AssignUserPermissions(ctx, userID, permissionIDs)
}

// RemoveUserPermissions 批量移除用户权限
func (s *rbacService) RemoveUserPermissions(ctx context.Context, userID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RemoveUserPermissions")
	defer span.End()

	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	return s.repo.RBAC().RemoveUserPermissions(ctx, userID, permissionIDs)
}

// RefreshUserPermissionCache 刷新用户权限缓存
func (s *rbacService) RefreshUserPermissionCache(ctx context.Context, userID uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RefreshUserPermissionCache")
	defer span.End()

	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// TODO: 实现权限缓存刷新逻辑
	s.logger.Info("刷新用户权限缓存", zap.Uint("user_id", userID))
	return nil
}

// BatchRefreshPermissionCache 批量刷新权限缓存
func (s *rbacService) BatchRefreshPermissionCache(ctx context.Context, userIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.BatchRefreshPermissionCache")
	defer span.End()

	span.SetAttributes(attribute.IntSlice("user.ids", intSliceFromUintSlice(userIDs)))

	for _, userID := range userIDs {
		err := s.RefreshUserPermissionCache(ctx, userID)
		if err != nil {
			s.logger.Error("刷新用户权限缓存失败", zap.Uint("user_id", userID), zap.Error(err))
		}
	}

	return nil
}

// AddPolicy 添加策略
func (s *rbacService) AddPolicy(ctx context.Context, subject, object, action string) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.AddPolicy")
	defer span.End()

	// TODO: 实现策略添加逻辑
	return nil
}

// RemovePolicy 移除策略
func (s *rbacService) RemovePolicy(ctx context.Context, subject, object, action string) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.RemovePolicy")
	defer span.End()

	// TODO: 实现策略移除逻辑
	return nil
}

// LoadPolicies 加载策略
func (s *rbacService) LoadPolicies(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.LoadPolicies")
	defer span.End()

	return s.SyncAllPermissions(ctx)
}

// SavePolicies 保存策略
func (s *rbacService) SavePolicies(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.SavePolicies")
	defer span.End()

	return s.casbinService.enforcer.SavePolicy()
}

// intSliceFromUintSlice 辅助函数：将uint切片转换为int切片
func intSliceFromUintSlice(uints []uint) []int {
	ints := make([]int, len(uints))
	for i, u := range uints {
		ints[i] = int(u)
	}
	return ints
}

// logUserPolicies 输出用户的所有权限策略用于调试
func (s *rbacService) logUserPolicies(ctx context.Context, userID uint) {
	if s.casbinService == nil {
		s.logger.Debug("Casbin服务未初始化，无法输出用户策略")
		return
	}

	subject := fmt.Sprintf("user:%d", userID)

	// 获取用户直接权限策略
	userPolicies, err := s.casbinService.GetEnforcer().GetFilteredPolicy(0, subject)
	if err != nil {
		s.logger.Error("获取用户直接权限策略失败", zap.Error(err))
	} else {
		s.logger.Debug("用户直接权限策略",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.Any("policies", userPolicies))
	}

	// 获取用户组成员关系
	userGroups, err := s.casbinService.GetEnforcer().GetRolesForUser(subject)
	if err != nil {
		s.logger.Error("获取用户组成员关系失败", zap.Error(err))
	} else {
		s.logger.Debug("用户组成员关系",
			zap.Uint("user_id", userID),
			zap.String("subject", subject),
			zap.Strings("groups", userGroups))

		// 获取用户组权限策略
		for _, group := range userGroups {
			groupPolicies, err := s.casbinService.GetEnforcer().GetFilteredNamedPolicy("p2", 0, group)
			if err != nil {
				s.logger.Error("获取用户组权限策略失败", zap.String("group", group), zap.Error(err))
			} else {
				s.logger.Debug("用户组权限策略",
					zap.Uint("user_id", userID),
					zap.String("group", group),
					zap.Any("policies", groupPolicies))
			}
		}
	}

	// 获取所有策略用于调试
	allPolicies, err := s.casbinService.GetEnforcer().GetPolicy()
	if err != nil {
		s.logger.Error("获取所有用户策略失败", zap.Error(err))
	}

	allGroupPolicies, err := s.casbinService.GetEnforcer().GetNamedPolicy("p2")
	if err != nil {
		s.logger.Error("获取所有用户组策略失败", zap.Error(err))
	}

	allGroupings, err := s.casbinService.GetEnforcer().GetGroupingPolicy()
	if err != nil {
		s.logger.Error("获取所有用户组成员关系失败", zap.Error(err))
	}

	s.logger.Debug("所有权限策略（调试用）",
		zap.Any("user_policies", allPolicies),
		zap.Any("group_policies", allGroupPolicies),
		zap.Any("group_memberships", allGroupings))
}

// InitSystemPermissions 初始化系统权限 - 按照technical-design.md 3.3.3节权限策略示例
// 该方法具有幂等性，可以安全地多次调用
func (s *rbacService) InitSystemPermissions(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.InitSystemPermissions")
	defer span.End()

	// 检查是否已经初始化过
	if s.initialized {
		s.logger.Debug("系统权限已初始化，跳过重复初始化")
		return nil
	}

	s.logger.Info("开始初始化系统权限...")

	// 1. 创建默认权限资源
	defaultPermissions := []*model.ResourcePermission{
		{
			Name:         "系统管理",
			ResourceType: "api",
			ResourcePath: "system:*",
			Actions:      "*",
			ScopeLevel:   "system",
			Description:  "系统级完全管理权限",
		},
	}

	// 2. 批量创建权限（如果不存在）
	for _, perm := range defaultPermissions {
		// 使用正确的路径格式检查权限是否存在
		existing, err := s.repo.RBAC().GetPermissionByPathAndActions(ctx, perm.ResourcePath, perm.Actions)
		if err != nil && err.Error() != "record not found" {
			s.logger.Error("检查权限是否存在失败", zap.String("path", perm.ResourcePath), zap.Error(err))
			continue
		}

		if existing == nil {
			perm.CreatedAt = time.Now()
			perm.UpdatedAt = time.Now()
			err = s.repo.RBAC().CreatePermission(ctx, perm)
			if err != nil {
				s.logger.Error("创建默认权限失败", zap.String("name", perm.Name), zap.Error(err))
				continue
			}
			s.logger.Info("创建默认权限成功", zap.String("name", perm.Name))
		} else {
			s.logger.Debug("默认权限已存在，跳过创建", zap.String("name", perm.Name))
		}
	}

	// 3. 确保admin用户组存在并分配权限
	adminGroup, err := s.repo.UserGroup().GetUserGroupByName(ctx, "admin")
	if err != nil {
		s.logger.Error("获取admin用户组失败", zap.Error(err))
		return fmt.Errorf("获取admin用户组失败: %w", err)
	}

	// 4. 确保admin用户被添加到admin用户组中
	adminUser, err := s.repo.User().GetUserByUsername(ctx, "admin")
	if err != nil {
		s.logger.Error("获取admin用户失败", zap.Error(err))
		return fmt.Errorf("获取admin用户失败: %w", err)
	}

	// 检查admin用户是否已经在admin用户组中
	isMember, err := s.repo.UserGroup().IsUserInGroup(ctx, adminUser.ID, adminGroup.ID)
	if err != nil {
		s.logger.Error("检查用户组成员关系失败", zap.Error(err))
	} else if !isMember {
		// 将admin用户添加到admin用户组
		err = s.repo.UserGroup().AddUserToGroup(ctx, adminUser.ID, adminGroup.ID)
		if err != nil {
			s.logger.Error("将admin用户添加到admin用户组失败", zap.Error(err))
		} else {
			s.logger.Info("admin用户已添加到admin用户组")
		}
	}

	// 5. 获取所有系统级权限并分配给admin用户组
	systemPermissions, _, err := s.repo.RBAC().ListPermissions(ctx, 1, 1000)
	if err != nil {
		s.logger.Error("获取系统权限列表失败", zap.Error(err))
		return fmt.Errorf("获取系统权限列表失败: %w", err)
	}

	// 为admin用户组分配所有系统权限
	var permissionIDs []uint
	for _, perm := range systemPermissions {
		if perm.ScopeLevel == "system" {
			permissionIDs = append(permissionIDs, perm.ID)
		}
	}

	if len(permissionIDs) > 0 {
		err = s.repo.RBAC().AssignGroupPermissions(ctx, adminGroup.ID, permissionIDs)
		if err != nil {
			s.logger.Error("为admin用户组分配权限失败", zap.Error(err))
			return fmt.Errorf("为admin用户组分配权限失败: %w", err)
		}
		s.logger.Info("为admin用户组分配权限成功", zap.Int("count", len(permissionIDs)))
	}

	// 6. 同步权限策略到Casbin
	err = s.SyncAllPermissions(ctx)
	if err != nil {
		s.logger.Error("同步权限策略失败", zap.Error(err))
		return fmt.Errorf("同步权限策略失败: %w", err)
	}

	// 标记初始化完成
	s.initialized = true
	s.logger.Info("系统权限初始化完成")
	return nil
}

// GetActiveRBACModel 获取活动RBAC模型
func (s *rbacService) GetActiveRBACModel(ctx context.Context) (*model.RBACModel, error) {
	ctx, span := s.tracer.Start(ctx, "RBACService.GetActiveRBACModel")
	defer span.End()

	return s.repo.RBAC().GetActiveRBACModel(ctx)
}

// CreateRBACModel 创建RBAC模型
func (s *rbacService) CreateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.CreateRBACModel")
	defer span.End()

	rbacModel.CreatedAt = time.Now()
	rbacModel.UpdatedAt = time.Now()

	return s.repo.RBAC().CreateRBACModel(ctx, rbacModel)
}

// UpdateRBACModel 更新RBAC模型
func (s *rbacService) UpdateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.UpdateRBACModel")
	defer span.End()

	rbacModel.UpdatedAt = time.Now()

	return s.repo.RBAC().UpdateRBACModel(ctx, rbacModel)
}

// SetActiveRBACModel 设置活动RBAC模型
func (s *rbacService) SetActiveRBACModel(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "RBACService.SetActiveRBACModel")
	defer span.End()

	return s.repo.RBAC().SetActiveRBACModel(ctx, id)
}

// GetEnforcer 获取Casbin执行器
func (s *rbacService) GetEnforcer() *casbin.Enforcer {
	if s.casbinService != nil {
		syncedEnforcer := s.casbinService.GetEnforcer()
		if syncedEnforcer != nil {
			// SyncedEnforcer 嵌入了 Enforcer，可以直接访问
			return syncedEnforcer.Enforcer
		}
	}
	return nil
}

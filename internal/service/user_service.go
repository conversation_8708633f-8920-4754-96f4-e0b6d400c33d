package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// userService 实现UserService接口
type userService struct {
	repo   repository.Repository
	tracer trace.Tracer
	logger logger.LoggerService
}

// NewUserService 创建用户服务实例
func NewUserService(repo repository.Repository, loggerSvc logger.LoggerService, tracer trace.Tracer) UserService {
	return &userService{
		repo:   repo,
		tracer: tracer,
		logger: loggerSvc,
	}
}

// GetUserByID 获取用户信息
func (s *userService) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.GetUserByID")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(id)))

	return s.repo.User().GetUserByID(ctx, id)
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(ctx context.Context, page, pageSize int) ([]*model.User, int64, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.ListUsers")
	defer span.End()
	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("pageSize", pageSize),
	)

	return s.repo.User().ListUsersPaginated(ctx, page, pageSize)
}

// CreateUser 创建用户
func (s *userService) CreateUser(ctx context.Context, req *model.UserCreateRequest) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.CreateUser")
	defer span.End()
	span.SetAttributes(attribute.String("user.username", req.Username))

	// 检查用户名是否已存在
	existingUser, err := s.repo.User().GetUserByUsername(ctx, req.Username)
	if err == nil && existingUser != nil {
		return nil, errors.New("username already exists")
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		existingUser, err := s.repo.User().GetUserByEmail(ctx, req.Email)
		if err == nil && existingUser != nil {
			return nil, errors.New("email already exists")
		}
	}

	// 创建用户对象 - 按照technical-design.md 4.3.1节设计
	user := &model.User{
		Username:         req.Username,
		Email:            req.Email,
		Nickname:         req.Name, // 使用Nickname字段
		Phone:            req.Phone,
		Avatar:           req.Avatar,
		Status:           1, // 1=启用
		IdentityProvider: "local",
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// 如果提供了密码，则哈希密码
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			span.RecordError(err)
			return nil, err
		}
		user.Password = string(hashedPassword)
	}

	err = s.repo.User().CreateUser(ctx, user)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return user, nil
}

// UpdateUser 更新用户
func (s *userService) UpdateUser(ctx context.Context, id uint, req *model.UserUpdateRequest) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.UpdateUser")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(id)))

	// 检查用户是否存在
	user, err := s.repo.User().GetUserByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// 更新字段
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Name != "" {
		user.Nickname = req.Name // 使用Nickname字段
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	user.Status = int(req.Status) // 转换UserStatus到int
	user.UpdatedAt = time.Now()

	err = s.repo.User().UpdateUser(ctx, user)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return user, nil
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "UserService.DeleteUser")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(id)))

	// 检查用户是否存在
	user, err := s.repo.User().GetUserByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	return s.repo.User().DeleteUser(ctx, id)
}

// GetUserByUsername 通过用户名获取用户
func (s *userService) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.GetUserByUsername")
	defer span.End()
	span.SetAttributes(attribute.String("user.username", username))

	return s.repo.User().GetUserByUsername(ctx, username)
}

// GetUserByEmail 通过Email获取用户
func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.GetUserByEmail")
	defer span.End()
	span.SetAttributes(attribute.String("user.email", email))

	return s.repo.User().GetUserByEmail(ctx, email)
}

// CountUsers 获取用户数量
func (s *userService) CountUsers(ctx context.Context) (int64, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.CountUsers")
	defer span.End()

	// 获取用户列表并计算数量
	_, total, err := s.repo.User().ListUsersPaginated(ctx, 1, 1)
	return total, err
}

// AuthenticateUser 通过用户名和密码验证用户
func (s *userService) AuthenticateUser(ctx context.Context, username, password string) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.AuthenticateUser")
	defer span.End()
	span.SetAttributes(attribute.String("user.username", username))

	user, err := s.repo.User().GetUserByUsername(ctx, username)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}

	// 检查用户状态
	if user.Status != 1 { // 1=启用
		return nil, errors.New("user is disabled")
	}

	// 使用bcrypt验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		span.RecordError(err)
		return nil, errors.New("invalid password")
	}

	// 更新最后登录时间
	_ = s.UpdateLastLogin(ctx, user.ID)

	// 不要返回密码
	user.Password = ""
	return user, nil
}

// GetUserByOIDCSubject 通过OIDC Subject获取用户
func (s *userService) GetUserByOIDCSubject(ctx context.Context, subject string) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.GetUserByOIDCSubject")
	defer span.End()
	span.SetAttributes(attribute.String("user.oidc_subject", subject))

	return s.repo.User().GetUserByOIDCSubject(ctx, subject)
}

// CreateOrUpdateOIDCUser 创建或更新OIDC用户
func (s *userService) CreateOrUpdateOIDCUser(ctx context.Context, userInfo *model.OIDCUserInfo) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.CreateOrUpdateOIDCUser")
	defer span.End()
	span.SetAttributes(attribute.String("user.oidc_subject", userInfo.Subject))

	// 先尝试通过OIDC Subject查找用户
	user, err := s.repo.User().GetUserByOIDCSubject(ctx, userInfo.Subject)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.RecordError(err)
		return nil, err
	}

	if user != nil {
		// 用户已存在，更新信息
		user.Email = userInfo.Email
		user.Nickname = userInfo.Name
		if userInfo.Picture != "" {
			user.Avatar = userInfo.Picture
		}
		user.FeishuOpenID = userInfo.FeishuOpenID
		user.FeishuUnionID = userInfo.FeishuUnionID
		user.FeishuUserID = userInfo.FeishuUserID
		user.UpdatedAt = time.Now()

		err = s.repo.User().UpdateUser(ctx, user)
		if err != nil {
			span.RecordError(err)
			return nil, err
		}
	} else {
		// 用户不存在，创建新用户
		user = &model.User{
			Username:         userInfo.PreferredUsername,
			Email:            userInfo.Email,
			Nickname:         userInfo.Name,
			Avatar:           userInfo.Picture,
			Status:           1, // 1=启用
			OIDCSubject:      userInfo.Subject,
			IdentityProvider: "oidc",
			FeishuOpenID:     userInfo.FeishuOpenID,
			FeishuUnionID:    userInfo.FeishuUnionID,
			FeishuUserID:     userInfo.FeishuUserID,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}

		err = s.repo.User().CreateUser(ctx, user)
		if err != nil {
			span.RecordError(err)
			return nil, err
		}
	}

	// TODO: 同步用户组（OIDCUserInfo中没有Groups字段）
	s.logger.Info("OIDC用户创建/更新完成", zap.String("username", user.Username))

	// 更新最后登录时间
	_ = s.UpdateLastLogin(ctx, user.ID)

	return user, nil
}

// UpdateUserStatus 更新用户状态
func (s *userService) UpdateUserStatus(ctx context.Context, userID uint, status model.UserStatus) error {
	ctx, span := s.tracer.Start(ctx, "UserService.UpdateUserStatus")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))
	span.SetAttributes(attribute.Int("user.status", int(status)))

	user, err := s.repo.User().GetUserByID(ctx, userID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	user.Status = int(status) // 转换UserStatus到int
	user.UpdatedAt = time.Now()

	return s.repo.User().UpdateUser(ctx, user)
}

// UpdateLastLogin 更新最后登录时间
func (s *userService) UpdateLastLogin(ctx context.Context, userID uint) error {
	ctx, span := s.tracer.Start(ctx, "UserService.UpdateLastLogin")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	now := time.Now()
	return s.repo.User().UpdateLastLogin(ctx, userID, &now)
}

// SyncUserFromOIDC 从OIDC同步用户信息
func (s *userService) SyncUserFromOIDC(ctx context.Context, userInfo *model.OIDCUserInfo) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.SyncUserFromOIDC")
	defer span.End()
	span.SetAttributes(attribute.String("user.oidc_subject", userInfo.Subject))

	return s.CreateOrUpdateOIDCUser(ctx, userInfo)
}

// SyncUserGroups 同步用户组
func (s *userService) SyncUserGroups(ctx context.Context, userID uint, groups []string) error {
	ctx, span := s.tracer.Start(ctx, "UserService.SyncUserGroups")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))
	span.SetAttributes(attribute.StringSlice("user.groups", groups))

	// 这里需要调用用户组服务来同步用户组
	// 暂时返回nil，具体实现在用户组服务中
	return nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, id uint, oldPassword, newPassword string) error {
	ctx, span := s.tracer.Start(ctx, "UserService.ChangePassword")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(id)))

	// 获取用户
	user, err := s.repo.User().GetUserByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return errors.New("invalid old password")
	}

	// 哈希新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 更新密码
	user.Password = string(hashedPassword)
	user.UpdatedAt = time.Now()

	return s.repo.User().UpdateUser(ctx, user)
}

// CreateOrUpdateOIDCUser 创建或更新OIDC用户

// GetUserByFeishuID 通过飞书ID获取用户
func (s *userService) GetUserByFeishuID(ctx context.Context, feishuOpenID, feishuUnionID string) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.GetUserByFeishuID")
	defer span.End()
	span.SetAttributes(
		attribute.String("feishu.open_id", feishuOpenID),
		attribute.String("feishu.union_id", feishuUnionID),
	)

	return s.repo.User().GetUserByFeishuID(ctx, feishuOpenID, feishuUnionID)
}

// SyncFeishuUser 同步飞书用户
func (s *userService) SyncFeishuUser(ctx context.Context, userInfo *model.OIDCUserInfo) (*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserService.SyncFeishuUser")
	defer span.End()

	// 设置身份提供者为飞书
	userInfo.Subject = fmt.Sprintf("feishu:%s", userInfo.FeishuOpenID)
	return s.CreateOrUpdateOIDCUser(ctx, userInfo)
}

// ActivateUser 激活用户
func (s *userService) ActivateUser(ctx context.Context, id uint) error {
	return s.updateUserStatus(ctx, id, model.UserStatusActive)
}

// DeactivateUser 停用用户
func (s *userService) DeactivateUser(ctx context.Context, id uint) error {
	return s.updateUserStatus(ctx, id, model.UserStatusInactive)
}

// LockUser 锁定用户
func (s *userService) LockUser(ctx context.Context, id uint) error {
	return s.updateUserStatus(ctx, id, model.UserStatusLocked)
}

// UnlockUser 解锁用户
func (s *userService) UnlockUser(ctx context.Context, id uint) error {
	return s.updateUserStatus(ctx, id, model.UserStatusActive)
}

// updateUserStatus 更新用户状态的内部方法
func (s *userService) updateUserStatus(ctx context.Context, id uint, status model.UserStatus) error {
	ctx, span := s.tracer.Start(ctx, "UserService.updateUserStatus")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("user.id", int64(id)),
		attribute.Int("user.status", int(status)),
	)

	user, err := s.repo.User().GetUserByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if user == nil {
		return errors.New("user not found")
	}

	user.Status = int(status) // 转换UserStatus到int
	user.UpdatedAt = time.Now()

	return s.repo.User().UpdateUser(ctx, user)
}

package service

import (
	"time"

	"kubeops/internal/auth"
)

// TokenPairResponse 新的Token对响应结构
type TokenPairResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
	
	// 用户信息
	User *UserInfo `json:"user"`
	
	// 会话信息
	SessionID string `json:"session_id"`
	LoginTime int64  `json:"login_time"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	UserID      uint     `json:"user_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	DisplayName string   `json:"display_name"`
	Avatar      string   `json:"avatar,omitempty"`
	Groups      []string `json:"groups"`      // 用户组列表
	Permissions []string `json:"permissions"` // 权限列表
	Status      string   `json:"status"`
	LastLogin   *int64   `json:"last_login,omitempty"`
}

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	DeviceID   string `json:"device_id"`
	DeviceType string `json:"device_type"` // web, mobile, desktop
	UserAgent  string `json:"user_agent"`
	IPAddress  string `json:"ip_address"`
	Platform   string `json:"platform,omitempty"`   // Windows, macOS, Linux, iOS, Android
	Browser    string `json:"browser,omitempty"`    // Chrome, Firefox, Safari, Edge
	Location   string `json:"location,omitempty"`   // 地理位置信息
}

// UserSession 用户会话信息
type UserSession struct {
	SessionID    string     `json:"session_id"`
	UserID       uint       `json:"user_id"`
	DeviceInfo   DeviceInfo `json:"device_info"`
	LoginTime    int64      `json:"login_time"`
	LastActivity int64      `json:"last_activity"`
	ExpiresAt    int64      `json:"expires_at"`
	IsActive     bool       `json:"is_active"`
	TokenID      string     `json:"token_id,omitempty"`
}

// UserClaims 用户声明信息（从JWT Claims转换而来）
type UserClaims struct {
	UserID       uint     `json:"user_id"`
	Username     string   `json:"username"`
	Email        string   `json:"email"`
	DisplayName  string   `json:"display_name"`
	Groups       []string `json:"groups"`      // 用户组列表
	Permissions  []string `json:"permissions"` // 权限列表
	SessionID    string   `json:"session_id"`
	TokenType    string   `json:"token_type"`
	TokenVersion int      `json:"token_version"`
	LoginTime    int64    `json:"login_time"`
	LastActivity int64    `json:"last_activity"`
	ExpiresAt    int64    `json:"expires_at"`

	// 设备信息
	DeviceID   string `json:"device_id,omitempty"`
	DeviceType string `json:"device_type,omitempty"`
	UserAgent  string `json:"user_agent,omitempty"`
	IPAddress  string `json:"ip_address,omitempty"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username   string     `json:"username" binding:"required"`
	Password   string     `json:"password" binding:"required"`
	RememberMe bool       `json:"remember_me"`
	DeviceInfo DeviceInfo `json:"device_info"`
}

// RefreshTokenRequest 刷新Token请求结构
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// LogoutRequest 登出请求结构
type LogoutRequest struct {
	LogoutAll bool `json:"logout_all"` // 是否登出所有设备
}

// SessionListResponse 会话列表响应
type SessionListResponse struct {
	Sessions    []*UserSession `json:"sessions"`
	TotalCount  int            `json:"total_count"`
	CurrentPage int            `json:"current_page"`
	PageSize    int            `json:"page_size"`
}

// LoginAttempt 登录尝试记录
type LoginAttempt struct {
	Username  string `json:"username"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	Success   bool   `json:"success"`
	Timestamp int64  `json:"timestamp"`
	Reason    string `json:"reason,omitempty"` // 失败原因
}

// AccountLockInfo 账户锁定信息
type AccountLockInfo struct {
	Username     string `json:"username"`
	IsLocked     bool   `json:"is_locked"`
	LockTime     int64  `json:"lock_time,omitempty"`
	UnlockTime   int64  `json:"unlock_time,omitempty"`
	AttemptCount int    `json:"attempt_count"`
	LastAttempt  int64  `json:"last_attempt,omitempty"`
}

// 转换函数：从新的JWT Claims转换为UserClaims
func ConvertJWTClaimsToUserClaims(jwtClaims *auth.JWTClaims) *UserClaims {
	if jwtClaims == nil {
		return nil
	}
	
	var expiresAt int64
	if jwtClaims.ExpiresAt != nil {
		expiresAt = jwtClaims.ExpiresAt.Unix()
	}
	
	return &UserClaims{
		UserID:       jwtClaims.UserID,
		Username:     jwtClaims.Username,
		Email:        jwtClaims.Email,
		DisplayName:  jwtClaims.DisplayName,
		Groups:       jwtClaims.Groups,
		Permissions:  jwtClaims.Permissions,
		SessionID:    jwtClaims.SessionID,
		TokenType:    jwtClaims.TokenType,
		TokenVersion: jwtClaims.TokenVersion,
		LoginTime:    jwtClaims.LoginTime,
		LastActivity: jwtClaims.LastActivity,
		ExpiresAt:    expiresAt,
		DeviceID:     jwtClaims.DeviceID,
		DeviceType:   jwtClaims.DeviceType,
		UserAgent:    jwtClaims.UserAgent,
		IPAddress:    jwtClaims.IPAddress,
	}
}

// 转换函数：从UserInfo转换为auth.UserInfo
func ConvertToAuthUserInfo(userInfo *UserInfo) *auth.UserInfo {
	if userInfo == nil {
		return nil
	}
	
	return &auth.UserInfo{
		UserID:      userInfo.UserID,
		Username:    userInfo.Username,
		Email:       userInfo.Email,
		DisplayName: userInfo.DisplayName,
		Groups:      userInfo.Groups,
		Permissions: userInfo.Permissions,
	}
}

// 转换函数：从DeviceInfo转换为auth.DeviceInfo
func ConvertToAuthDeviceInfo(deviceInfo *DeviceInfo) *auth.DeviceInfo {
	if deviceInfo == nil {
		return nil
	}
	
	return &auth.DeviceInfo{
		DeviceID:   deviceInfo.DeviceID,
		DeviceType: deviceInfo.DeviceType,
		UserAgent:  deviceInfo.UserAgent,
		IPAddress:  deviceInfo.IPAddress,
	}
}

// 转换函数：从auth.TokenPair转换为TokenPairResponse
func ConvertTokenPairToResponse(tokenPair *auth.TokenPair, userInfo *UserInfo, sessionID string) *TokenPairResponse {
	if tokenPair == nil {
		return nil
	}
	
	return &TokenPairResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		TokenType:    tokenPair.TokenType,
		ExpiresIn:    tokenPair.ExpiresIn,
		User:         userInfo,
		SessionID:    sessionID,
		LoginTime:    time.Now().Unix(),
	}
}



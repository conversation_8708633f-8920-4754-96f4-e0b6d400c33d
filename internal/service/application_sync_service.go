package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/tools/cache"

	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"
)

// ApplicationSyncService 应用同步服务
type ApplicationSyncService struct {
	repo           repository.Repository
	clusterManager *k8s.ClusterManager
	redisClient    *redis.Client
	logger         logger.LoggerService
	tracer         trace.Tracer

	// 同步状态管理
	syncConfigs   map[uint]*model.ApplicationSyncConfig // 集群同步配置
	informerStops map[uint]chan struct{}                // Informer停止信号
}

// NewApplicationSyncService 创建应用同步服务
func NewApplicationSyncService(
	repo repository.Repository,
	clusterManager *k8s.ClusterManager,
	redisClient *redis.Client,
	loggerSvc logger.LoggerService,
	tracer trace.Tracer,
) *ApplicationSyncService {
	return &ApplicationSyncService{
		repo:           repo,
		clusterManager: clusterManager,
		redisClient:    redisClient,
		logger:         loggerSvc,
		tracer:         tracer,
		syncConfigs:    make(map[uint]*model.ApplicationSyncConfig),
		informerStops:  make(map[uint]chan struct{}),
	}
}

// StartApplicationSync 启动应用同步
func (s *ApplicationSyncService) StartApplicationSync(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationSyncService.StartApplicationSync")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 检查是否已经在同步
	if _, exists := s.informerStops[clusterID]; exists {
		return fmt.Errorf("application sync already started for cluster %d", clusterID)
	}

	// 获取集群信息
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if cluster == nil {
		return fmt.Errorf("cluster %d not found", clusterID)
	}

	// 获取K8s客户端
	k8sClient, err := s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("get cluster client failed: %w", err)
	}

	// 获取所有已创建项目的命名空间列表
	projectNamespaces, err := s.getProjectNamespaces(ctx)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("get project namespaces failed: %w", err)
	}

	// 创建同步配置
	syncConfig := &model.ApplicationSyncConfig{
		ClusterID:         clusterID,
		EnabledNamespaces: projectNamespaces,
		ExcludedNamespaces: []string{
			"kube-system", "kube-public", "kube-node-lease",
			"istio-system", "monitoring", "logging", "default",
		},
		WorkloadTypes: []string{"deployment", "statefulset", "daemonset", "job", "cronjob"},
		SyncInterval:  30,
		AutoComplete:  false,
	}
	s.syncConfigs[clusterID] = syncConfig

	// 创建停止信号
	stopCh := make(chan struct{})
	s.informerStops[clusterID] = stopCh

	// 启动Informer
	go s.startInformers(ctx, clusterID, k8sClient, syncConfig, stopCh)

	s.logger.Info("Application sync started",
		zap.Uint("cluster_id", clusterID),
		zap.Strings("project_namespaces", projectNamespaces),
	)
	return nil
}

// StopApplicationSync 停止应用同步
func (s *ApplicationSyncService) StopApplicationSync(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationSyncService.StopApplicationSync")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 检查是否在同步
	stopCh, exists := s.informerStops[clusterID]
	if !exists {
		return fmt.Errorf("application sync not started for cluster %d", clusterID)
	}

	// 发送停止信号
	close(stopCh)

	// 清理资源
	delete(s.informerStops, clusterID)
	delete(s.syncConfigs, clusterID)

	s.logger.Info("Application sync stopped", zap.Uint("cluster_id", clusterID))
	return nil
}

// getProjectNamespaces 获取所有已创建项目的命名空间列表
func (s *ApplicationSyncService) getProjectNamespaces(ctx context.Context) ([]string, error) {
	// 获取所有活跃项目
	projects, _, err := s.repo.Project().ListProjectsPaginated(ctx, 1, 1000) // 假设项目数量不会超过1000
	if err != nil {
		return nil, fmt.Errorf("failed to get projects: %w", err)
	}

	var namespaces []string
	for _, project := range projects {
		// 项目名称直接对应命名空间名称
		if project.Status == model.ProjectStatusActive {
			namespaces = append(namespaces, project.Name)
		}
	}

	s.logger.Info("Retrieved project namespaces",
		zap.Int("count", len(namespaces)),
		zap.Strings("namespaces", namespaces),
	)

	return namespaces, nil
}

// startInformers 启动Informer
func (s *ApplicationSyncService) startInformers(ctx context.Context, clusterID uint, k8sClient k8s.K8sClient, config *model.ApplicationSyncConfig, stopCh chan struct{}) {
	// 为每个项目命名空间创建独立的InformerFactory
	for _, namespace := range config.EnabledNamespaces {
		// 检查是否在排除列表中
		if s.isNamespaceExcluded(namespace, config.ExcludedNamespaces) {
			continue
		}

		// 为特定命名空间创建InformerFactory
		factory := informers.NewSharedInformerFactoryWithOptions(
			k8sClient.GetClient(),
			time.Duration(config.SyncInterval)*time.Second,
			informers.WithNamespace(namespace),
		)

		// 启动不同类型的工作负载Informer
		for _, workloadType := range config.WorkloadTypes {
			switch workloadType {
			case "deployment":
				s.startDeploymentInformer(ctx, clusterID, factory, stopCh, namespace)
			case "statefulset":
				s.startStatefulSetInformer(ctx, clusterID, factory, stopCh, namespace)
			case "daemonset":
				s.startDaemonSetInformer(ctx, clusterID, factory, stopCh, namespace)
			case "job":
				s.startJobInformer(ctx, clusterID, factory, stopCh, namespace)
			case "cronjob":
				s.startCronJobInformer(ctx, clusterID, factory, stopCh, namespace)
			}
		}

		// 启动Informer
		factory.Start(stopCh)

		// 等待缓存同步
		factory.WaitForCacheSync(stopCh)

		s.logger.Info("Informers started for namespace",
			zap.Uint("cluster_id", clusterID),
			zap.String("namespace", namespace),
		)
	}

	s.logger.Info("All informers started and synced",
		zap.Uint("cluster_id", clusterID),
		zap.Strings("namespaces", config.EnabledNamespaces),
	)

	// 等待停止信号
	<-stopCh
	s.logger.Info("Informers stopped", zap.Uint("cluster_id", clusterID))
}

// isNamespaceExcluded 检查命名空间是否在排除列表中
func (s *ApplicationSyncService) isNamespaceExcluded(namespace string, excludedNamespaces []string) bool {
	for _, excluded := range excludedNamespaces {
		if namespace == excluded {
			return true
		}
	}
	return false
}

// startDeploymentInformer 启动Deployment Informer
func (s *ApplicationSyncService) startDeploymentInformer(ctx context.Context, clusterID uint, factory informers.SharedInformerFactory, stopCh chan struct{}, namespace string) {
	informer := factory.Apps().V1().Deployments().Informer()

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			deployment := obj.(*appsv1.Deployment)
			s.handleWorkloadEvent(ctx, clusterID, "deployment", deployment.Namespace, deployment.Name, "ADDED", deployment)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			deployment := newObj.(*appsv1.Deployment)
			s.handleWorkloadEvent(ctx, clusterID, "deployment", deployment.Namespace, deployment.Name, "MODIFIED", deployment)
		},
		DeleteFunc: func(obj interface{}) {
			deployment := obj.(*appsv1.Deployment)
			s.handleWorkloadEvent(ctx, clusterID, "deployment", deployment.Namespace, deployment.Name, "DELETED", deployment)
		},
	})
}

// startStatefulSetInformer 启动StatefulSet Informer
func (s *ApplicationSyncService) startStatefulSetInformer(ctx context.Context, clusterID uint, factory informers.SharedInformerFactory, stopCh chan struct{}, namespace string) {
	informer := factory.Apps().V1().StatefulSets().Informer()

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			sts := obj.(*appsv1.StatefulSet)
			s.handleWorkloadEvent(ctx, clusterID, "statefulset", sts.Namespace, sts.Name, "ADDED", sts)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			sts := newObj.(*appsv1.StatefulSet)
			s.handleWorkloadEvent(ctx, clusterID, "statefulset", sts.Namespace, sts.Name, "MODIFIED", sts)
		},
		DeleteFunc: func(obj interface{}) {
			sts := obj.(*appsv1.StatefulSet)
			s.handleWorkloadEvent(ctx, clusterID, "statefulset", sts.Namespace, sts.Name, "DELETED", sts)
		},
	})
}

// startDaemonSetInformer 启动DaemonSet Informer
func (s *ApplicationSyncService) startDaemonSetInformer(ctx context.Context, clusterID uint, factory informers.SharedInformerFactory, stopCh chan struct{}, namespace string) {
	informer := factory.Apps().V1().DaemonSets().Informer()

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			ds := obj.(*appsv1.DaemonSet)
			s.handleWorkloadEvent(ctx, clusterID, "daemonset", ds.Namespace, ds.Name, "ADDED", ds)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			ds := newObj.(*appsv1.DaemonSet)
			s.handleWorkloadEvent(ctx, clusterID, "daemonset", ds.Namespace, ds.Name, "MODIFIED", ds)
		},
		DeleteFunc: func(obj interface{}) {
			ds := obj.(*appsv1.DaemonSet)
			s.handleWorkloadEvent(ctx, clusterID, "daemonset", ds.Namespace, ds.Name, "DELETED", ds)
		},
	})
}

// startJobInformer 启动Job Informer
func (s *ApplicationSyncService) startJobInformer(ctx context.Context, clusterID uint, factory informers.SharedInformerFactory, stopCh chan struct{}, namespace string) {
	informer := factory.Batch().V1().Jobs().Informer()

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			job := obj.(*batchv1.Job)
			s.handleWorkloadEvent(ctx, clusterID, "job", job.Namespace, job.Name, "ADDED", job)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			job := newObj.(*batchv1.Job)
			s.handleWorkloadEvent(ctx, clusterID, "job", job.Namespace, job.Name, "MODIFIED", job)
		},
		DeleteFunc: func(obj interface{}) {
			job := obj.(*batchv1.Job)
			s.handleWorkloadEvent(ctx, clusterID, "job", job.Namespace, job.Name, "DELETED", job)
		},
	})
}

// startCronJobInformer 启动CronJob Informer
func (s *ApplicationSyncService) startCronJobInformer(ctx context.Context, clusterID uint, factory informers.SharedInformerFactory, stopCh chan struct{}, namespace string) {
	informer := factory.Batch().V1().CronJobs().Informer()

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			cronjob := obj.(*batchv1.CronJob)
			s.handleWorkloadEvent(ctx, clusterID, "cronjob", cronjob.Namespace, cronjob.Name, "ADDED", cronjob)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			cronjob := newObj.(*batchv1.CronJob)
			s.handleWorkloadEvent(ctx, clusterID, "cronjob", cronjob.Namespace, cronjob.Name, "MODIFIED", cronjob)
		},
		DeleteFunc: func(obj interface{}) {
			cronjob := obj.(*batchv1.CronJob)
			s.handleWorkloadEvent(ctx, clusterID, "cronjob", cronjob.Namespace, cronjob.Name, "DELETED", cronjob)
		},
	})
}

// handleWorkloadEvent 处理工作负载事件
func (s *ApplicationSyncService) handleWorkloadEvent(ctx context.Context, clusterID uint, workloadType, namespace, name, eventType string, obj runtime.Object) {
	// 使用Redis分布式锁防止多副本重复处理
	lockKey := fmt.Sprintf("workload_event_lock:%d:%s:%s:%s", clusterID, namespace, workloadType, name)

	// 尝试获取锁
	acquired, err := s.redisClient.SetNX(ctx, lockKey, "locked", 30*time.Second).Result()
	if err != nil || !acquired {
		// 获取锁失败，跳过处理
		return
	}
	defer s.redisClient.Del(ctx, lockKey)

	// 创建工作负载事件
	event := s.createWorkloadEvent(clusterID, namespace, workloadType, name, eventType, obj)

	// 发布事件到Redis Stream
	s.publishWorkloadEvent(ctx, event)

	// 异步处理事件
	go s.processWorkloadEvent(context.Background(), event)
}

// createWorkloadEvent 创建工作负载事件
func (s *ApplicationSyncService) createWorkloadEvent(clusterID uint, namespace, workloadType, name, eventType string, obj runtime.Object) *model.WorkloadEvent {
	event := &model.WorkloadEvent{
		ClusterID:    clusterID,
		Namespace:    namespace,
		WorkloadType: workloadType,
		WorkloadName: name,
		EventType:    eventType,
		Timestamp:    time.Now(),
	}

	// 提取对象信息
	if metaObj, ok := obj.(metav1.Object); ok {
		event.ResourceVersion = metaObj.GetResourceVersion()
		event.Labels = metaObj.GetLabels()
		event.Annotations = metaObj.GetAnnotations()
	}

	return event
}

// publishWorkloadEvent 发布工作负载事件到Redis Stream
func (s *ApplicationSyncService) publishWorkloadEvent(ctx context.Context, event *model.WorkloadEvent) {
	streamKey := "workload_events"

	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal workload event", zap.Error(err))
		return
	}

	_, err = s.redisClient.XAdd(ctx, &redis.XAddArgs{
		Stream: streamKey,
		Values: map[string]interface{}{
			"event": string(eventData),
		},
	}).Result()

	if err != nil {
		s.logger.Error("Failed to publish workload event", zap.Error(err))
	}
}

// processWorkloadEvent 处理工作负载事件
func (s *ApplicationSyncService) processWorkloadEvent(ctx context.Context, event *model.WorkloadEvent) {
	ctx, span := s.tracer.Start(ctx, "ApplicationSyncService.processWorkloadEvent")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(event.ClusterID)),
		attribute.String("namespace", event.Namespace),
		attribute.String("workload.type", event.WorkloadType),
		attribute.String("workload.name", event.WorkloadName),
		attribute.String("event.type", event.EventType),
	)

	switch event.EventType {
	case "ADDED", "MODIFIED":
		err := s.syncWorkloadToApplication(ctx, event)
		if err != nil {
			s.logger.Error("Failed to sync workload to application",
				zap.Error(err),
				zap.Uint("cluster_id", event.ClusterID),
				zap.String("workload", fmt.Sprintf("%s/%s/%s", event.Namespace, event.WorkloadType, event.WorkloadName)),
			)
		}
	case "DELETED":
		err := s.handleWorkloadDeletion(ctx, event)
		if err != nil {
			s.logger.Error("Failed to handle workload deletion",
				zap.Error(err),
				zap.Uint("cluster_id", event.ClusterID),
				zap.String("workload", fmt.Sprintf("%s/%s/%s", event.Namespace, event.WorkloadType, event.WorkloadName)),
			)
		}
	}
}

// syncWorkloadToApplication 同步工作负载到应用
func (s *ApplicationSyncService) syncWorkloadToApplication(ctx context.Context, event *model.WorkloadEvent) error {
	// 通过命名空间名称直接查找对应项目
	project, err := s.repo.Project().GetProjectByName(ctx, event.Namespace)
	if err != nil {
		// 项目不存在，跳过同步
		s.logger.Debug("Project not found for namespace",
			zap.String("namespace", event.Namespace),
			zap.Uint("cluster_id", event.ClusterID),
		)
		return nil
	}

	// 查找或创建应用
	app, err := s.repo.Application().GetApplicationByWorkload(ctx, project.ID, event.WorkloadType, event.WorkloadName)
	if err != nil {
		// 应用不存在，创建新应用
		app = &model.Application{
			Name:         event.WorkloadName,
			ProjectID:    project.ID,
			WorkloadType: event.WorkloadType,
			WorkloadName: event.WorkloadName,
			Namespace:    event.Namespace,
			Status:       model.ApplicationStatusActive,
			SyncStatus:   model.ApplicationSyncStatusPending, // 待完善
			Source:       model.ApplicationSourceDiscovered,
			IsComplete:   false,
			Labels:       event.Labels,
			Annotations:  event.Annotations,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		err = s.repo.Application().CreateApplication(ctx, app)
		if err != nil {
			return fmt.Errorf("failed to create application: %w", err)
		}

		s.logger.Info("Created new application from workload",
			zap.String("application", app.Name),
			zap.Uint("project_id", project.ID),
			zap.String("namespace", event.Namespace),
			zap.String("workload_type", event.WorkloadType),
		)
	} else {
		// 更新现有应用
		app.Labels = event.Labels
		app.Annotations = event.Annotations
		app.UpdatedAt = time.Now()

		err = s.repo.Application().UpdateApplication(ctx, app)
		if err != nil {
			return fmt.Errorf("failed to update application: %w", err)
		}

		s.logger.Debug("Updated existing application",
			zap.String("application", app.Name),
			zap.Uint("project_id", project.ID),
		)
	}

	// 查找项目在该集群的部署记录
	projectCluster, err := s.getOrCreateProjectCluster(ctx, project.ID, event.ClusterID, event.Namespace)
	if err != nil {
		return fmt.Errorf("failed to get or create project cluster: %w", err)
	}

	// 更新或创建应用集群部署记录
	appCluster, err := s.repo.Application().GetApplicationCluster(ctx, app.ID, projectCluster.ID)
	if err != nil {
		// 创建新的部署记录
		appCluster = &model.ApplicationCluster{
			ApplicationID:    app.ID,
			ProjectClusterID: projectCluster.ID,
			Status:           model.ApplicationClusterStatusRunning,
			ResourceVersion:  event.ResourceVersion,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}

		err = s.repo.Application().CreateApplicationCluster(ctx, appCluster)
		if err != nil {
			return fmt.Errorf("failed to create application cluster: %w", err)
		}
	} else {
		// 更新部署记录
		appCluster.ResourceVersion = event.ResourceVersion
		appCluster.Status = model.ApplicationClusterStatusRunning
		appCluster.UpdatedAt = time.Now()

		err = s.repo.Application().UpdateApplicationCluster(ctx, appCluster)
		if err != nil {
			return fmt.Errorf("failed to update application cluster: %w", err)
		}
	}

	return nil
}

// getOrCreateProjectCluster 获取或创建项目集群关联
func (s *ApplicationSyncService) getOrCreateProjectCluster(ctx context.Context, projectID, clusterID uint, namespace string) (*model.ProjectCluster, error) {
	// 查找现有的项目集群关联
	projectClusters, err := s.repo.Project().GetProjectClusters(ctx, projectID)
	if err != nil {
		return nil, err
	}

	// 查找匹配的集群
	for _, pc := range projectClusters {
		if pc.ClusterID == clusterID && pc.Namespace == namespace {
			return pc, nil
		}
	}

	// 不存在则创建新的项目集群关联
	projectCluster := &model.ProjectCluster{
		ProjectID: projectID,
		ClusterID: clusterID,
		Namespace: namespace,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = s.repo.Project().CreateProjectCluster(ctx, projectCluster)
	if err != nil {
		return nil, err
	}

	s.logger.Info("Created project cluster association",
		zap.Uint("project_id", projectID),
		zap.Uint("cluster_id", clusterID),
		zap.String("namespace", namespace),
	)

	return projectCluster, nil
}

// handleWorkloadDeletion 处理工作负载删除
func (s *ApplicationSyncService) handleWorkloadDeletion(ctx context.Context, event *model.WorkloadEvent) error {
	// 通过命名空间名称直接查找对应项目
	project, err := s.repo.Project().GetProjectByName(ctx, event.Namespace)
	if err != nil {
		// 项目不存在，跳过处理
		s.logger.Debug("Project not found for namespace during deletion",
			zap.String("namespace", event.Namespace),
			zap.Uint("cluster_id", event.ClusterID),
		)
		return nil
	}

	// 查找对应的应用
	app, err := s.repo.Application().GetApplicationByWorkload(ctx, project.ID, event.WorkloadType, event.WorkloadName)
	if err != nil {
		// 应用不存在，跳过处理
		s.logger.Debug("Application not found during workload deletion",
			zap.String("workload", fmt.Sprintf("%s/%s", event.WorkloadType, event.WorkloadName)),
			zap.String("namespace", event.Namespace),
		)
		return nil
	}

	// 查找项目集群关联
	projectClusters, err := s.repo.Project().GetProjectClusters(ctx, project.ID)
	if err != nil {
		return fmt.Errorf("failed to get project clusters: %w", err)
	}

	var targetProjectCluster *model.ProjectCluster
	for _, pc := range projectClusters {
		if pc.ClusterID == event.ClusterID && pc.Namespace == event.Namespace {
			targetProjectCluster = pc
			break
		}
	}

	if targetProjectCluster != nil {
		// 删除应用集群部署记录
		err = s.repo.Application().DeleteApplicationCluster(ctx, app.ID, targetProjectCluster.ID)
		if err != nil {
			s.logger.Error("Failed to delete application cluster", zap.Error(err))
		} else {
			s.logger.Info("Deleted application cluster deployment",
				zap.String("application", app.Name),
				zap.Uint("cluster_id", event.ClusterID),
				zap.String("namespace", event.Namespace),
			)
		}

		// 如果应用是自动发现的且没有其他集群部署，删除应用
		if app.Source == model.ApplicationSourceDiscovered {
			clusters, err := s.repo.Application().GetApplicationClusters(ctx, app.ID)
			if err == nil && len(clusters) == 0 {
				// 没有其他集群部署，删除应用
				err = s.repo.Application().DeleteApplication(ctx, app.ID)
				if err != nil {
					s.logger.Error("Failed to delete discovered application", zap.Error(err))
				} else {
					s.logger.Info("Deleted discovered application with no deployments",
						zap.String("application", app.Name),
						zap.Uint("project_id", project.ID),
					)
				}
			}
		}
	}

	return nil
}

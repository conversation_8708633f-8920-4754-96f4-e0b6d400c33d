package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	"kubeops/internal/auth"
	"kubeops/internal/config"
	"kubeops/internal/logger"
	"kubeops/internal/model"
	"kubeops/internal/oidc"
	"kubeops/internal/redis"
	"kubeops/internal/repository"
)

// AuthService 认证服务接口 - 重构版本
type AuthService interface {
	// 新的双Token认证机制
	Login(ctx context.Context, username, password string, deviceInfo *DeviceInfo) (*TokenPairResponse, error)
	LoginWithRememberMe(ctx context.Context, username, password string, deviceInfo *DeviceInfo, rememberMe bool) (*TokenPairResponse, error)

	// 登出 - 支持Token撤销
	Logout(ctx context.Context, token string, logoutAll bool) error

	// Token管理
	RefreshToken(ctx context.Context, refreshToken string) (*TokenPairResponse, error)
	VerifyToken(ctx context.Context, token string) (*UserClaims, error)
	RevokeToken(ctx context.Context, token string, reason string) error
	RevokeUserTokens(ctx context.Context, userID uint, reason string) error

	// 会话管理
	GetUserSessions(ctx context.Context, userID uint) ([]*UserSession, error)
	RevokeSession(ctx context.Context, userID uint, sessionID string) error
	GetActiveSessionCount(ctx context.Context, userID uint) (int, error)

	// 登录安全
	CheckLoginAttempts(ctx context.Context, username, ipAddress string) (*AccountLockInfo, error)
	RecordLoginAttempt(ctx context.Context, username, ipAddress, userAgent string, success bool, reason string) error
	UnlockAccount(ctx context.Context, username string) error



	// OIDC相关方法
	GetOIDCService() oidc.Service
	GetOIDCAuthURL() string
	GetOIDCAuthURLWithState(state string) string
	HandleOIDCCallback(ctx context.Context, code string) (*TokenPairResponse, error)
	UpdateOIDCConfig(config *oidc.OIDCConfig)
}

// AuthServiceImpl 认证服务实现 - 清理版本
type AuthServiceImpl struct {
	repo         repository.Repository
	log          logger.LoggerService
	tracer       trace.Tracer
	config       *config.Config
	redisManager redis.RedisManager

	// JWT服务
	jwtService auth.JWTService  // 新的JWT服务

	// RBAC服务
	rbacService RBACService // RBAC权限服务

	// OIDC服务
	oidcService oidc.Service
	oidcLock    sync.Mutex // 添加锁用于并发保护
}

// NewAuthService 创建认证服务 - 清理版本
func NewAuthService(
	repo repository.Repository,
	loggerSvc logger.LoggerService,
	tracer trace.Tracer,
	cfg *config.Config,
	redisManager redis.RedisManager,
	jwtService auth.JWTService,
	rbacService RBACService,
	oidcConfig *oidc.OIDCConfig,
) (AuthService, error) {
	// 创建OIDC服务
	oidcService, err := oidc.NewService(oidcConfig, loggerSvc.GetLogger())
	if err != nil {
		loggerSvc.Warn("初始化OIDC服务失败，OIDC登录功能将不可用", zap.Error(err))
		oidcService = nil
	}

	return &AuthServiceImpl{
		repo:         repo,
		log:          loggerSvc,
		tracer:       tracer,
		config:       cfg,
		redisManager: redisManager,
		jwtService:   jwtService,
		rbacService:  rbacService,
		oidcService:  oidcService,
	}, nil
}

// Login 用户登录 - 新的双Token机制
func (s *AuthServiceImpl) Login(ctx context.Context, username, password string, deviceInfo *DeviceInfo) (*TokenPairResponse, error) {
	ctx, span := s.tracer.Start(ctx, "AuthService.Login.New")
	defer span.End()

	// 检查登录尝试限制
	lockInfo, err := s.CheckLoginAttempts(ctx, username, deviceInfo.IPAddress)
	if err != nil {
		s.log.Error("检查登录尝试失败", zap.Error(err))
	}
	if lockInfo != nil && lockInfo.IsLocked {
		return nil, fmt.Errorf("账户已被锁定，请在 %d 分钟后重试",
			(lockInfo.UnlockTime-time.Now().Unix())/60)
	}

	// 查询用户
	user, err := s.repo.User().GetUserByUsername(ctx, username)
	if err != nil {
		// 记录失败的登录尝试
		s.RecordLoginAttempt(ctx, username, deviceInfo.IPAddress, deviceInfo.UserAgent, false, "用户不存在")
		return nil, errors.New("用户名或密码错误")
	}

	// 检查用户是否是OIDC用户
	if user.OIDCSubject != "" && user.Password == "" {
		s.RecordLoginAttempt(ctx, username, deviceInfo.IPAddress, deviceInfo.UserAgent, false, "OIDC用户使用密码登录")
		return nil, errors.New("该用户使用OIDC登录，请使用相应的身份提供者登录")
	}

	// 验证密码
	if !validatePassword(user.Password, password) {
		s.RecordLoginAttempt(ctx, username, deviceInfo.IPAddress, deviceInfo.UserAgent, false, "密码错误")
		return nil, errors.New("用户名或密码错误")
	}

	// 检查用户状态
	if user.Status != 1 {
		s.RecordLoginAttempt(ctx, username, deviceInfo.IPAddress, deviceInfo.UserAgent, false, "账户被禁用")
		return nil, errors.New("用户账户已被禁用")
	}

	// 获取用户组
	groups, err := s.getUserGroups(ctx, user.ID)
	if err != nil {
		s.log.Error("获取用户组失败", zap.Error(err))
		groups = []string{}
	}

	// 获取用户权限（基于用户组）
	permissions, err := s.getUserPermissions(ctx, user.ID, groups)
	if err != nil {
		s.log.Error("获取用户权限失败", zap.Error(err))
		permissions = []string{}
	}

	// 构建用户信息
	userInfo := &UserInfo{
		UserID:      user.ID,
		Username:    user.Username,
		Email:       user.Email,
		DisplayName: user.Nickname, // 使用新的Nickname字段
		Avatar:      user.Avatar,
		Groups:      groups,
		Permissions: permissions,
		Status:      s.convertUserStatus(user.Status), // 直接使用int类型
	}

	// 生成Token对
	authUserInfo := ConvertToAuthUserInfo(userInfo)
	authDeviceInfo := ConvertToAuthDeviceInfo(deviceInfo)

	tokenPair, err := s.jwtService.GenerateTokenPair(ctx, authUserInfo, authDeviceInfo)
	if err != nil {
		s.log.Error("生成Token失败", zap.Error(err))
		return nil, errors.New("登录失败，请重试")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	if err := s.repo.User().UpdateUser(ctx, user); err != nil {
		s.log.Error("更新用户最后登录时间失败", zap.Error(err))
	}

	// 记录成功的登录尝试
	s.RecordLoginAttempt(ctx, username, deviceInfo.IPAddress, deviceInfo.UserAgent, true, "")

	// 构建响应
	response := ConvertTokenPairToResponse(tokenPair, userInfo, "")
	if response != nil {
		// 从JWT Claims中提取SessionID
		claims, err := s.jwtService.VerifyAccessToken(ctx, tokenPair.AccessToken)
		if err == nil {
			response.SessionID = claims.SessionID
		}

		lastLogin := now.Unix()
		userInfo.LastLogin = &lastLogin
		response.User.LastLogin = &lastLogin
	}

	return response, nil
}

// LoginWithRememberMe 用户登录（支持记住我功能）
func (s *AuthServiceImpl) LoginWithRememberMe(ctx context.Context, username, password string, deviceInfo *DeviceInfo, rememberMe bool) (*TokenPairResponse, error) {
	// 调用标准登录方法
	response, err := s.Login(ctx, username, password, deviceInfo)
	if err != nil {
		return nil, err
	}

	// 如果启用了记住我功能，生成长期Token
	if rememberMe && response != nil {
		// 重新生成带有更长过期时间的Token
		userInfo := ConvertToAuthUserInfo(response.User)
		authDeviceInfo := ConvertToAuthDeviceInfo(deviceInfo)

		// 这里可以使用特殊的记住我Token配置
		// 暂时使用标准Token，实际应用中可以配置更长的过期时间
		tokenPair, err := s.jwtService.GenerateTokenPair(ctx, userInfo, authDeviceInfo)
		if err != nil {
			s.log.Error("生成记住我Token失败", zap.Error(err))
			return response, nil // 返回标准Token
		}

		response.AccessToken = tokenPair.AccessToken
		response.RefreshToken = tokenPair.RefreshToken
		response.ExpiresIn = tokenPair.ExpiresIn
	}

	return response, nil
}



// Logout 用户登出
func (s *AuthServiceImpl) Logout(ctx context.Context, token string, logoutAll bool) error {
	ctx, span := s.tracer.Start(ctx, "AuthService.Logout")
	defer span.End()

	// 验证Token并获取用户信息
	claims, err := s.jwtService.VerifyAccessToken(ctx, token)
	if err != nil {
		return fmt.Errorf("无效的Token: %w", err)
	}

	if logoutAll {
		// 撤销用户的所有Token
		err = s.jwtService.RevokeUserTokens(ctx, claims.UserID, "user_logout_all")
		if err != nil {
			s.log.Error("撤销用户所有Token失败",
				zap.Uint("user_id", claims.UserID),
				zap.Error(err))
			return fmt.Errorf("登出失败: %w", err)
		}

		s.log.Info("用户登出所有设备",
			zap.Uint("user_id", claims.UserID),
			zap.String("username", claims.Username))
	} else {
		// 只撤销当前Token
		err = s.jwtService.RevokeToken(ctx, token, "user_logout")
		if err != nil {
			s.log.Error("撤销Token失败",
				zap.String("token_id", claims.ID),
				zap.Error(err))
			return fmt.Errorf("登出失败: %w", err)
		}

		s.log.Info("用户登出",
			zap.Uint("user_id", claims.UserID),
			zap.String("username", claims.Username),
			zap.String("session_id", claims.SessionID))
	}

	return nil
}

// RefreshToken 刷新Token
func (s *AuthServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*TokenPairResponse, error) {
	ctx, span := s.tracer.Start(ctx, "AuthService.RefreshToken")
	defer span.End()

	// 使用新的JWT服务刷新Token
	tokenPair, err := s.jwtService.RefreshToken(ctx, refreshToken)
	if err != nil {
		return nil, fmt.Errorf("刷新Token失败: %w", err)
	}

	// 验证新的Access Token以获取用户信息
	claims, err := s.jwtService.VerifyAccessToken(ctx, tokenPair.AccessToken)
	if err != nil {
		s.log.Error("验证刷新后的Token失败", zap.Error(err))
		return nil, fmt.Errorf("Token刷新异常")
	}

	// 获取用户信息
	user, err := s.repo.User().GetUserByID(ctx, claims.UserID)
	if err != nil {
		s.log.Error("获取用户信息失败",
			zap.Uint("user_id", claims.UserID),
			zap.Error(err))
		return nil, fmt.Errorf("获取用户信息失败")
	}

	// 构建用户信息
	userInfo := &UserInfo{
		UserID:      user.ID,
		Username:    user.Username,
		Email:       user.Email,
		DisplayName: user.Nickname,
		Avatar:      user.Avatar,
		Groups:      claims.Groups,
		Permissions: claims.Permissions,
		Status:      s.convertUserStatus(user.Status),
	}

	if user.LastLogin != nil {
		lastLogin := user.LastLogin.Unix()
		userInfo.LastLogin = &lastLogin
	}

	// 构建响应
	response := ConvertTokenPairToResponse(tokenPair, userInfo, claims.SessionID)

	return response, nil
}

// VerifyToken 验证JWT令牌
func (s *AuthServiceImpl) VerifyToken(ctx context.Context, token string) (*UserClaims, error) {
	claims, err := s.jwtService.VerifyAccessToken(ctx, token)
	if err != nil {
		return nil, err
	}

	return ConvertJWTClaimsToUserClaims(claims), nil
}

// RevokeToken 撤销Token
func (s *AuthServiceImpl) RevokeToken(ctx context.Context, token string, reason string) error {
	return s.jwtService.RevokeToken(ctx, token, reason)
}

// RevokeUserTokens 撤销用户的所有Token
func (s *AuthServiceImpl) RevokeUserTokens(ctx context.Context, userID uint, reason string) error {
	return s.jwtService.RevokeUserTokens(ctx, userID, reason)
}

// GetUserSessions 获取用户会话列表
func (s *AuthServiceImpl) GetUserSessions(ctx context.Context, userID uint) ([]*UserSession, error) {
	ctx, span := s.tracer.Start(ctx, "AuthService.GetUserSessions")
	defer span.End()

	// 从Redis中获取用户的活跃会话
	sessionPattern := fmt.Sprintf("auth:activity:user:%d:session:*", userID)
	sessionKeys, err := s.redisManager.Keys(ctx, sessionPattern)
	if err != nil {
		return nil, fmt.Errorf("获取会话列表失败: %w", err)
	}

	var sessions []*UserSession
	for _, sessionKey := range sessionKeys {
		sessionData, err := s.redisManager.HGetAll(ctx, sessionKey)
		if err != nil {
			s.log.Warn("获取会话数据失败", zap.String("session_key", sessionKey), zap.Error(err))
			continue
		}

		session := s.parseSessionData(sessionData)
		if session != nil {
			sessions = append(sessions, session)
		}
	}

	return sessions, nil
}

// RevokeSession 撤销指定会话
func (s *AuthServiceImpl) RevokeSession(ctx context.Context, userID uint, sessionID string) error {
	ctx, span := s.tracer.Start(ctx, "AuthService.RevokeSession")
	defer span.End()

	// 获取会话信息
	sessionKey := fmt.Sprintf("auth:activity:user:%d:session:%s", userID, sessionID)
	sessionData, err := s.redisManager.HGetAll(ctx, sessionKey)
	if err != nil {
		return fmt.Errorf("获取会话信息失败: %w", err)
	}

	if len(sessionData) == 0 {
		return fmt.Errorf("会话不存在")
	}

	// 获取Token ID并撤销
	if tokenID, exists := sessionData["token_id"]; exists && tokenID != "" {
		// 构建黑名单键并撤销Token
		blacklistKey := fmt.Sprintf("auth:blacklist:%s", tokenID)
		blacklistData := map[string]interface{}{
			"token_id":       tokenID,
			"user_id":        userID,
			"reason":         "session_revoked",
			"blacklisted_at": time.Now().Unix(),
			"session_id":     sessionID,
		}

		for field, value := range blacklistData {
			if err := s.redisManager.HSet(ctx, blacklistKey, field, value); err != nil {
				return fmt.Errorf("撤销Token失败: %w", err)
			}
		}

		// 设置过期时间
		ttl := s.config.RedisModules.AuthSecurity.TTL.JWTBlacklist
		s.redisManager.Expire(ctx, blacklistKey, ttl)
	}

	// 删除会话信息
	err = s.redisManager.Del(ctx, sessionKey)
	if err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}

	s.log.Info("会话已撤销",
		zap.Uint("user_id", userID),
		zap.String("session_id", sessionID))

	return nil
}

// GetActiveSessionCount 获取用户活跃会话数量
func (s *AuthServiceImpl) GetActiveSessionCount(ctx context.Context, userID uint) (int, error) {
	sessionPattern := fmt.Sprintf("auth:activity:user:%d:session:*", userID)
	sessionKeys, err := s.redisManager.Keys(ctx, sessionPattern)
	if err != nil {
		return 0, fmt.Errorf("获取会话数量失败: %w", err)
	}

	return len(sessionKeys), nil
}



// CheckLoginAttempts 检查登录尝试限制
func (s *AuthServiceImpl) CheckLoginAttempts(ctx context.Context, username, ipAddress string) (*AccountLockInfo, error) {
	attemptsKey := fmt.Sprintf("auth:attempts:%s:%s", username, ipAddress)

	// 获取尝试次数
	attempts, err := s.redisManager.Get(ctx, attemptsKey)
	if err != nil && err != redis.ErrKeyNotFound {
		return nil, err
	}

	attemptCount := 0
	if attempts != "" {
		if count, err := strconv.Atoi(attempts); err == nil {
			attemptCount = count
		}
	}

	maxAttempts := s.config.RedisModules.AuthSecurity.Security.MaxLoginAttempts
	lockDuration := s.config.RedisModules.AuthSecurity.Security.LockoutDuration

	lockInfo := &AccountLockInfo{
		Username:     username,
		AttemptCount: attemptCount,
		IsLocked:     attemptCount >= maxAttempts,
	}

	if lockInfo.IsLocked {
		// 获取锁定时间
		ttl, err := s.redisManager.TTL(ctx, attemptsKey)
		if err == nil && ttl > 0 {
			lockInfo.UnlockTime = time.Now().Add(ttl).Unix()
		} else {
			lockInfo.UnlockTime = time.Now().Add(lockDuration).Unix()
		}
	}

	return lockInfo, nil
}

// RecordLoginAttempt 记录登录尝试
func (s *AuthServiceImpl) RecordLoginAttempt(ctx context.Context, username, ipAddress, userAgent string, success bool, reason string) error {
	attemptsKey := fmt.Sprintf("auth:attempts:%s:%s", username, ipAddress)

	if success {
		// 登录成功，清除尝试记录
		s.redisManager.Del(ctx, attemptsKey)

		// 记录成功的登录历史
		historyKey := fmt.Sprintf("auth:history:%s", username)
		loginRecord := map[string]interface{}{
			"ip_address": ipAddress,
			"user_agent": userAgent,
			"success":    true,
			"timestamp":  time.Now().Unix(),
		}

		// 使用List存储登录历史，保留最近50条
		for field, value := range loginRecord {
			s.redisManager.HSet(ctx, historyKey+":"+fmt.Sprintf("%d", time.Now().Unix()), field, value)
		}

		return nil
	}

	// 登录失败，增加尝试次数
	attempts, err := s.redisManager.Get(ctx, attemptsKey)
	if err != nil && err != redis.ErrKeyNotFound {
		return err
	}

	attemptCount := 1
	if attempts != "" {
		if count, err := strconv.Atoi(attempts); err == nil {
			attemptCount = count + 1
		}
	}

	// 设置尝试次数
	lockDuration := s.config.RedisModules.AuthSecurity.Security.LockoutDuration
	err = s.redisManager.Set(ctx, attemptsKey, fmt.Sprintf("%d", attemptCount), lockDuration)
	if err != nil {
		return err
	}

	// 记录失败的登录历史
	historyKey := fmt.Sprintf("auth:history:%s", username)
	loginRecord := map[string]interface{}{
		"ip_address": ipAddress,
		"user_agent": userAgent,
		"success":    false,
		"reason":     reason,
		"timestamp":  time.Now().Unix(),
	}

	for field, value := range loginRecord {
		s.redisManager.HSet(ctx, historyKey+":"+fmt.Sprintf("%d", time.Now().Unix()), field, value)
	}

	return nil
}

// UnlockAccount 解锁账户
func (s *AuthServiceImpl) UnlockAccount(ctx context.Context, username string) error {
	// 删除所有相关的尝试记录
	pattern := fmt.Sprintf("auth:attempts:%s:*", username)
	keys, err := s.redisManager.Keys(ctx, pattern)
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		err = s.redisManager.Del(ctx, keys...)
		if err != nil {
			return err
		}
	}

	s.log.Info("账户已解锁", zap.String("username", username))
	return nil
}







// GetOIDCService 获取OIDC服务
func (s *AuthServiceImpl) GetOIDCService() oidc.Service {
	return s.oidcService
}

// GetOIDCAuthURL 获取OIDC认证URL
func (s *AuthServiceImpl) GetOIDCAuthURL() string {
	if s.oidcService == nil {
		return ""
	}
	// 使用时间戳作为state，实际环境中应使用更安全的随机值并进行验证
	state := fmt.Sprintf("%d", time.Now().Unix())
	return s.oidcService.GetAuthURL(state)
}

// GetOIDCAuthURLWithState 获取带有指定state参数的OIDC认证URL
func (s *AuthServiceImpl) GetOIDCAuthURLWithState(state string) string {
	if s.oidcService == nil {
		return ""
	}
	return s.oidcService.GetAuthURL(state)
}

// HandleOIDCCallback 处理OIDC回调
func (s *AuthServiceImpl) HandleOIDCCallback(ctx context.Context, code string) (*TokenPairResponse, error) {
	ctx, span := s.tracer.Start(ctx, "AuthService.HandleOIDCCallback")
	defer span.End()

	if s.oidcService == nil {
		return nil, errors.New("OIDC服务未配置")
	}

	// 通过OIDC服务处理回调
	authResult, err := s.oidcService.HandleCallback(ctx, code)
	if err != nil {
		return nil, err
	}

	claims := authResult.Claims
	if claims == nil || claims.Subject == "" {
		return nil, errors.New("无效的OIDC响应，缺少必要的用户信息")
	}

	// 查找或创建用户
	user, err := s.findOrCreateOIDCUser(ctx, claims)
	if err != nil {
		return nil, err
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLogin = &now
	if err := s.repo.User().UpdateUser(ctx, user); err != nil {
		s.log.Warn("更新用户最后登录时间失败", zap.Error(err))
	}

	// 获取用户组
	groups, err := s.getUserGroups(ctx, user.ID)
	if err != nil {
		s.log.Error("获取用户组失败", zap.Error(err))
		groups = []string{}
	}

	// 获取用户权限（基于用户组）
	permissions, err := s.getUserPermissions(ctx, user.ID, groups)
	if err != nil {
		s.log.Error("获取用户权限失败", zap.Error(err))
		permissions = []string{}
	}

	// 构建用户信息
	userInfo := &UserInfo{
		UserID:      user.ID,
		Username:    user.Username,
		Email:       user.Email,
		DisplayName: user.Nickname,
		Avatar:      user.Avatar,
		Groups:      groups,
		Permissions: permissions,
		Status:      s.convertUserStatus(int(user.Status)),
	}

	// 构建设备信息（OIDC登录）
	deviceInfo := &DeviceInfo{
		DeviceID:   "oidc-device",
		DeviceType: "web",
		UserAgent:  "OIDC Login",
		IPAddress:  "0.0.0.0", // 这里可以从context中获取真实IP
	}

	// 生成Token对
	authUserInfo := ConvertToAuthUserInfo(userInfo)
	authDeviceInfo := ConvertToAuthDeviceInfo(deviceInfo)

	tokenPair, err := s.jwtService.GenerateTokenPair(ctx, authUserInfo, authDeviceInfo)
	if err != nil {
		s.log.Error("生成Token失败", zap.Error(err))
		return nil, errors.New("登录失败，请重试")
	}

	// 构建响应
	response := ConvertTokenPairToResponse(tokenPair, userInfo, "")
	if response != nil {
		// 从JWT Claims中提取SessionID
		jwtClaims, err := s.jwtService.VerifyAccessToken(ctx, tokenPair.AccessToken)
		if err == nil {
			response.SessionID = jwtClaims.SessionID
		}

		lastLogin := now.Unix()
		userInfo.LastLogin = &lastLogin
		response.User.LastLogin = &lastLogin
	}

	return response, nil
}

// findOrCreateOIDCUser 查找或创建OIDC用户
func (s *AuthServiceImpl) findOrCreateOIDCUser(ctx context.Context, claims *oidc.OIDCClaims) (*model.User, error) {
	// 首先通过OIDC Subject查询用户
	user, err := s.repo.User().GetUserByOIDCSubject(ctx, claims.Subject)

	// 从claims.Raw中提取飞书相关字段
	feishuOpenID := getStringFromClaims(claims.Raw, "feishu_open_id")
	feishuUnionID := getStringFromClaims(claims.Raw, "feishu_union_id")
	feishuUserID := getStringFromClaims(claims.Raw, "feishu_user_id")

	// 记录所有的自定义字段，帮助调试
	s.log.Debug("从OIDC claims中提取的自定义字段",
		zap.Any("raw_claims", claims.Raw),
		zap.String("feishu_open_id", feishuOpenID),
		zap.String("feishu_union_id", feishuUnionID),
		zap.String("feishu_user_id", feishuUserID))

	if err == nil {
		// 用户存在，更新用户信息
		if user.Email == "" && claims.Email != "" {
			user.Email = claims.Email
		}
		if user.Nickname == "" && claims.Name != "" {
			user.Nickname = claims.Name
		}
		if user.Avatar == "" && claims.Picture != "" {
			user.Avatar = claims.Picture
		}

		// 更新飞书相关信息
		if feishuOpenID != "" {
			user.FeishuOpenID = feishuOpenID
		}
		if feishuUnionID != "" {
			user.FeishuUnionID = feishuUnionID
		}
		if feishuUserID != "" {
			user.FeishuUserID = feishuUserID
		}

		// 更新用户组映射
		if err := s.syncUserGroups(ctx, user, claims.Groups); err != nil {
			s.log.Warn("同步用户组失败", zap.Error(err))
		}

		if err := s.repo.User().UpdateUser(ctx, user); err != nil {
			return nil, err
		}
		return user, nil
	}

	// 如果未找到，尝试通过邮箱查找
	if claims.Email != "" {
		user, err := s.repo.User().GetUserByEmail(ctx, claims.Email)
		if err == nil {
			// 用户存在，更新OIDC信息
			user.OIDCSubject = claims.Subject
			if user.Nickname == "" && claims.Name != "" {
				user.Nickname = claims.Name
			}
			if user.Avatar == "" && claims.Picture != "" {
				user.Avatar = claims.Picture
			}

			// 更新飞书相关信息
			if feishuOpenID != "" {
				user.FeishuOpenID = feishuOpenID
			}
			if feishuUnionID != "" {
				user.FeishuUnionID = feishuUnionID
			}
			if feishuUserID != "" {
				user.FeishuUserID = feishuUserID
			}

			// 更新用户组映射
			if err := s.syncUserGroups(ctx, user, claims.Groups); err != nil {
				s.log.Warn("同步用户组失败", zap.Error(err))
			}

			if err := s.repo.User().UpdateUser(ctx, user); err != nil {
				return nil, err
			}
			return user, nil
		}
	}

	// 创建新用户
	username := s.generateUsername(claims)
	newUser := &model.User{
		Username:         username,
		Email:            claims.Email,
		Nickname:         claims.Name,
		Avatar:           claims.Picture,
		OIDCSubject:      claims.Subject,
		IdentityProvider: "keycloak",
		Status:           1, // 正常状态
		// 设置飞书相关字段
		FeishuOpenID:  feishuOpenID,
		FeishuUnionID: feishuUnionID,
		FeishuUserID:  feishuUserID,
		// OIDC用户不设置密码
		Password: "",
	}

	// 记录创建用户的详细信息，帮助调试
	s.log.Debug("尝试创建OIDC用户",
		zap.String("username", newUser.Username),
		zap.String("email", newUser.Email),
		zap.String("oidc_subject", newUser.OIDCSubject),
		zap.String("feishu_open_id", newUser.FeishuOpenID),
		zap.String("feishu_union_id", newUser.FeishuUnionID),
		zap.String("feishu_user_id", newUser.FeishuUserID))

	// 保存新用户
	if err := s.repo.User().CreateUser(ctx, newUser); err != nil {
		return nil, fmt.Errorf("创建OIDC用户失败: %w", err)
	}

	// 同步用户组
	if err := s.syncUserGroups(ctx, newUser, claims.Groups); err != nil {
		s.log.Warn("同步新用户的用户组失败", zap.Error(err))
	}

	s.log.Info("成功创建OIDC用户",
		zap.String("username", newUser.Username),
		zap.String("email", newUser.Email),
		zap.String("oidc_subject", newUser.OIDCSubject))

	return newUser, nil
}

// generateUsername 生成用户名
func (s *AuthServiceImpl) generateUsername(claims *oidc.OIDCClaims) string {
	// 优先使用邮箱前缀作为用户名
	if claims.Email != "" {
		for i := 0; i < len(claims.Email); i++ {
			if claims.Email[i] == '@' {
				return claims.Email[:i]
			}
		}
	}

	// 如果没有邮箱或提取失败，使用Name
	if claims.Name != "" {
		return claims.Name
	}

	// 如果Name也没有，使用Subject的一部分
	if len(claims.Subject) > 8 {
		return claims.Subject[:8]
	}

	return claims.Subject
}

// UpdateOIDCConfig 更新OIDC配置
func (s *AuthServiceImpl) UpdateOIDCConfig(config *oidc.OIDCConfig) {
	if config == nil {
		return
	}

	// 使用锁保护操作
	s.oidcLock.Lock()
	defer s.oidcLock.Unlock()

	s.log.Info("正在更新OIDC配置",
		zap.Bool("enabled", config.Enabled),
		zap.String("issuer_url", config.IssuerURL),
		zap.String("client_id", config.ClientID),
		zap.Bool("has_client_secret", config.ClientSecret != ""),
		zap.String("redirect_uri", config.RedirectURI))

	if s.oidcService != nil {
		// 更新现有服务的配置
		s.oidcService.UpdateConfig(config)
	} else if config.Enabled {
		// 如果OIDC服务不存在但配置已启用，尝试创建服务
		oidcService, err := oidc.NewService(config, s.log.GetLogger())
		if err != nil {
			s.log.Error("更新OIDC配置失败，无法创建服务", zap.Error(err))
			return
		}
		s.oidcService = oidcService
		s.log.Info("OIDC服务创建成功")
	}

	s.log.Info("OIDC配置已成功更新并初始化",
		zap.String("issuer", config.IssuerURL),
		zap.String("client_id", config.ClientID),
		zap.String("redirect_uri", config.RedirectURI))
}

// getStringFromClaims 从OIDC声明中获取字符串值
func getStringFromClaims(claims map[string]interface{}, key string) string {
	if value, ok := claims[key]; ok {
		if strValue, ok := value.(string); ok {
			return strValue
		}
	}
	return ""
}

// syncUserGroups 同步用户组
func (s *AuthServiceImpl) syncUserGroups(ctx context.Context, user *model.User, keycloakGroups []string) error {
	if len(keycloakGroups) == 0 {
		return nil
	}

	// 获取系统配置，这里假设使用系统配置仓库获取映射
	// 如果没有Config仓库，可以使用其他方式存储映射关系，如数据库表
	// 这里可以实现根据Keycloak组到平台用户组的映射

	// 硬编码一些映射关系作为示例
	// 实际项目中，应该从数据库或配置文件获取
	groupMappings := map[string]string{
		"/上海金盈子/技术开发部/grafanaadmin": "Grafana管理员",
		"/上海金盈子/技术开发部/infraadmin":   "基础设施管理员",
		"/上海金盈子/技术开发部/业务研发组":        "业务研发组",
	}

	s.log.Info("开始同步用户组",
		zap.Uint("user_id", user.ID),
		zap.String("username", user.Username),
		zap.Strings("keycloak_groups", keycloakGroups))

	// 获取用户组仓库
	userGroupRepo := s.repo.UserGroup()

	// 当前用户所属的用户组
	userGroups, err := userGroupRepo.GetUserGroups(ctx, user.ID)
	if err != nil {
		return fmt.Errorf("获取用户当前组失败: %w", err)
	}

	// 记录当前用户所属的用户组ID
	existingGroupIDs := make(map[uint]bool)
	for _, group := range userGroups {
		existingGroupIDs[group.ID] = true
	}

	// 用于存储需要添加的用户组ID
	var targetGroupIDs []uint

	// 获取所有用户组，用于匹配
	allGroups, _, err := userGroupRepo.ListUserGroupsPaginated(ctx, 1, 1000)
	if err != nil {
		return fmt.Errorf("获取所有用户组失败: %w", err)
	}

	// 建立用户组名称到ID的映射
	nameToGroup := make(map[string]*model.UserGroup)
	for _, group := range allGroups {
		nameToGroup[group.Name] = group
	}

	// 遍历Keycloak组，寻找匹配的平台组
	for _, keycloakGroup := range keycloakGroups {
		// 检查直接映射
		if platformGroupName, ok := groupMappings[keycloakGroup]; ok {
			// 查找对应的平台用户组
			if platformGroup, exists := nameToGroup[platformGroupName]; exists {
				targetGroupIDs = append(targetGroupIDs, platformGroup.ID)
				s.log.Debug("映射Keycloak组到平台组",
					zap.String("keycloak_group", keycloakGroup),
					zap.String("platform_group", platformGroupName),
					zap.Uint("group_id", platformGroup.ID))
			} else {
				// 如果组不存在，创建新组
				newGroup := &model.UserGroup{
					Name:        platformGroupName,
					Description: fmt.Sprintf("从Keycloak映射的组: %s", keycloakGroup),
				}
				if err := userGroupRepo.CreateUserGroup(ctx, newGroup); err != nil {
					s.log.Warn("创建平台用户组失败",
						zap.String("group_name", platformGroupName),
						zap.Error(err))
					continue
				}
				targetGroupIDs = append(targetGroupIDs, newGroup.ID)
				s.log.Info("创建了新的平台用户组",
					zap.String("keycloak_group", keycloakGroup),
					zap.String("platform_group", platformGroupName),
					zap.Uint("group_id", newGroup.ID))
			}
		}
	}

	// 如果没有找到任何匹配的组，直接返回
	if len(targetGroupIDs) == 0 {
		s.log.Debug("用户没有匹配的平台用户组",
			zap.Strings("keycloak_groups", keycloakGroups))
		return nil
	}

	// 添加新的用户组关系
	for _, groupID := range targetGroupIDs {
		if !existingGroupIDs[groupID] {
			if err := userGroupRepo.AddUserToGroup(ctx, user.ID, groupID); err != nil {
				s.log.Warn("添加用户到组失败",
					zap.Uint("user_id", user.ID),
					zap.Uint("group_id", groupID),
					zap.Error(err))
			} else {
				s.log.Info("添加用户到组成功",
					zap.Uint("user_id", user.ID),
					zap.Uint("group_id", groupID))
			}
		}
	}

	s.log.Info("成功同步用户组",
		zap.Uint("user_id", user.ID),
		zap.String("username", user.Username),
		zap.Uints("group_ids", targetGroupIDs))

	return nil
}

// 新增的辅助方法

// getUserPermissions 获取用户权限（基于用户组）
func (s *AuthServiceImpl) getUserPermissions(ctx context.Context, userID uint, groups []string) ([]string, error) {
	// 使用RBAC服务获取用户权限
	if s.rbacService != nil {
		permissions, err := s.rbacService.GetUserPermissions(ctx, userID)
		if err != nil {
			s.log.Error("从RBAC服务获取用户权限失败", zap.Uint("user_id", userID), zap.Error(err))
			return []string{}, err
		}
		return permissions, nil
	}

	// 如果RBAC服务不可用，返回空权限列表
	s.log.Warn("RBAC服务不可用，返回空权限列表", zap.Uint("user_id", userID))
	return []string{}, nil
}

// getUserGroups 获取用户组
func (s *AuthServiceImpl) getUserGroups(ctx context.Context, userID uint) ([]string, error) {
	// 获取用户组仓库
	userGroupRepo := s.repo.UserGroup()

	// 获取用户所属的用户组
	userGroups, err := userGroupRepo.GetUserGroups(ctx, userID)
	if err != nil {
		s.log.Error("获取用户组失败", zap.Uint("user_id", userID), zap.Error(err))
		return []string{}, err
	}

	// 提取用户组名称
	groupNames := make([]string, len(userGroups))
	for i, group := range userGroups {
		groupNames[i] = group.Name
	}

	s.log.Debug("获取用户组成功", zap.Uint("user_id", userID), zap.Strings("groups", groupNames))
	return groupNames, nil
}

// convertUserStatus 转换用户状态
func (s *AuthServiceImpl) convertUserStatus(status int) string {
	switch status {
	case 1:
		return "active"
	case 0:
		return "inactive"
	case -1:
		return "banned"
	default:
		return "unknown"
	}
}

// validatePassword 验证密码 - 使用bcrypt验证
func validatePassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

// parseSessionData 解析会话数据
func (s *AuthServiceImpl) parseSessionData(sessionData map[string]string) *UserSession {
	if len(sessionData) == 0 {
		return nil
	}

	session := &UserSession{}

	if sessionID, exists := sessionData["session_id"]; exists {
		session.SessionID = sessionID
	}

	if userIDStr, exists := sessionData["user_id"]; exists {
		if userID, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			session.UserID = uint(userID)
		}
	}

	if loginTimeStr, exists := sessionData["login_time"]; exists {
		if loginTime, err := strconv.ParseInt(loginTimeStr, 10, 64); err == nil {
			session.LoginTime = loginTime
		}
	}

	if lastActivityStr, exists := sessionData["last_activity"]; exists {
		if lastActivity, err := strconv.ParseInt(lastActivityStr, 10, 64); err == nil {
			session.LastActivity = lastActivity
		}
	}

	// 设备信息
	session.DeviceInfo = DeviceInfo{
		DeviceID:   sessionData["device_id"],
		DeviceType: sessionData["device_type"],
		UserAgent:  sessionData["user_agent"],
		IPAddress:  sessionData["ip_address"],
	}

	session.TokenID = sessionData["token_id"]
	session.IsActive = true // 如果能查到就说明是活跃的

	return session
}

package model

import (
	"time"
)

// SystemOIDCConfig OIDC配置模型
type SystemOIDCConfig struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	Enabled      bool      `json:"enabled"`
	IssuerURL    string    `gorm:"size:255" json:"issuer_url"`    // Keycloak实例URL
	ClientID     string    `gorm:"size:100" json:"client_id"`     // OIDC客户端ID
	ClientSecret string    `gorm:"size:255" json:"client_secret"` // OIDC客户端密钥
	RedirectURI  string    `gorm:"size:255" json:"redirect_uri"`  // 回调URI
	Scopes       string    `gorm:"size:255" json:"scopes"`        // 请求的scope，逗号分隔
	GroupsClaim  string    `gorm:"size:100" json:"groups_claim"`  // Keycloak中的组声明字段
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// SystemFeishuConfig 飞书配置模型
type SystemFeishuConfig struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	AppID     string    `gorm:"size:100" json:"app_id"`
	AppSecret string    `gorm:"size:255" json:"app_secret"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SystemOBSConfig OBS配置模型
type SystemOBSConfig struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	Enabled       bool      `json:"enabled"`
	Endpoint      string    `gorm:"size:255" json:"endpoint"`
	AccessKey     string    `gorm:"size:100" json:"access_key"`
	SecretKey     string    `gorm:"size:255" json:"secret_key"`
	Bucket        string    `gorm:"size:100" json:"bucket"`
	Region        string    `gorm:"size:50" json:"region"`
	EncryptionKey string    `gorm:"size:255" json:"encryption_key"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// SystemAuditConfig 审计配置模型
type SystemAuditConfig struct {
	ID                uint      `gorm:"primaryKey" json:"id"`
	RetentionDays     int       `gorm:"default:90" json:"retention_days"`        // 数据库保留天数
	ArchiveEnabled    bool      `gorm:"default:false" json:"archive_enabled"`    // 是否启用归档
	ArchiveInterval   string    `gorm:"size:20" json:"archive_interval"`         // 归档间隔：quarterly, monthly
	EncryptionEnabled bool      `gorm:"default:false" json:"encryption_enabled"` // 是否启用加密
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// SystemBasicConfig 系统基本配置模型
type SystemBasicConfig struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	SystemName   string    `gorm:"size:100" json:"system_name"`     // 系统名称
	SystemLogo   string    `gorm:"type:text" json:"system_logo"`    // 系统Logo (Base64或URL)
	ContactEmail string    `gorm:"size:100" json:"contact_email"`   // 系统联系邮箱
	Version      string    `gorm:"size:50" json:"version"`          // 系统版本号
	DebugMode    bool      `gorm:"default:false" json:"debug_mode"` // 是否开启调试模式
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// SystemConfig 统一系统配置模型，包含所有配置项作为直接属性
type SystemConfig struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// OIDC配置
	OIDCEnabled      bool   `gorm:"column:oidc_enabled" json:"oidc_enabled"`             // 是否启用OIDC
	OIDCIssuerURL    string `gorm:"column:oidc_issuer_url" json:"oidc_issuer_url"`       // OIDC颁发者URL
	OIDCClientID     string `gorm:"column:oidc_client_id" json:"oidc_client_id"`         // OIDC客户端ID
	OIDCClientSecret string `gorm:"column:oidc_client_secret" json:"oidc_client_secret"` // OIDC客户端密钥
	OIDCRedirectURI  string `gorm:"column:oidc_redirect_uri" json:"oidc_redirect_uri"`   // OIDC回调URI
	OIDCScopes       string `gorm:"column:oidc_scopes" json:"oidc_scopes"`               // OIDC请求的scope
	OIDCGroupsClaim  string `gorm:"column:oidc_groups_claim" json:"oidc_groups_claim"`   // OIDC用户组声明

	// 飞书配置
	FeishuAppID     string `gorm:"column:feishu_app_id" json:"feishu_app_id"`         // 飞书应用ID
	FeishuAppSecret string `gorm:"column:feishu_app_secret" json:"feishu_app_secret"` // 飞书应用密钥

	// 对象存储(OBS)配置
	OBSEnabled       bool   `gorm:"column:obs_enabled" json:"obs_enabled"`               // 是否启用对象存储
	OBSEndpoint      string `gorm:"column:obs_endpoint" json:"obs_endpoint"`             // OBS端点地址
	OBSAccessKey     string `gorm:"column:obs_access_key" json:"obs_access_key"`         // OBS访问密钥ID
	OBSSecretKey     string `gorm:"column:obs_secret_key" json:"obs_secret_key"`         // OBS访问密钥
	OBSBucket        string `gorm:"column:obs_bucket" json:"obs_bucket"`                 // OBS存储桶名称
	OBSRegion        string `gorm:"column:obs_region" json:"obs_region"`                 // OBS区域
	OBSEncryptionKey string `gorm:"column:obs_encryption_key" json:"obs_encryption_key"` // OBS加密密钥

	// 审计配置
	AuditRetentionDays   int    `gorm:"column:audit_retention_days" json:"audit_retention_days"`     // 审计日志保留天数
	AuditArchiveInterval string `gorm:"column:audit_archive_interval" json:"audit_archive_interval"` // 审计日志归档间隔

	// 系统一般配置
	SystemName         string `gorm:"column:system_name" json:"system_name"`                   // 系统名称
	SystemLogo         string `gorm:"column:system_logo;type:text" json:"system_logo"`         // 系统Logo (Base64或URL)
	SystemContactEmail string `gorm:"column:system_contact_email" json:"system_contact_email"` // 系统联系邮箱
	SystemVersion      string `gorm:"column:system_version" json:"system_version"`             // 系统版本号
	SystemDebugMode    bool   `gorm:"column:system_debug_mode" json:"system_debug_mode"`       // 是否开启调试模式
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// GetOIDCConfig 获取OIDC配置
func (c *SystemConfig) GetOIDCConfig() *SystemOIDCConfig {
	return &SystemOIDCConfig{
		Enabled:      c.OIDCEnabled,
		IssuerURL:    c.OIDCIssuerURL,
		ClientID:     c.OIDCClientID,
		ClientSecret: c.OIDCClientSecret,
		RedirectURI:  c.OIDCRedirectURI,
		Scopes:       c.OIDCScopes,
		GroupsClaim:  c.OIDCGroupsClaim,
	}
}

// GetFeishuConfig 获取飞书配置
func (c *SystemConfig) GetFeishuConfig() *SystemFeishuConfig {
	return &SystemFeishuConfig{
		AppID:     c.FeishuAppID,
		AppSecret: c.FeishuAppSecret,
	}
}

// GetOBSConfig 获取对象存储配置
func (c *SystemConfig) GetOBSConfig() *SystemOBSConfig {
	return &SystemOBSConfig{
		Enabled:       c.OBSEnabled,
		Endpoint:      c.OBSEndpoint,
		AccessKey:     c.OBSAccessKey,
		SecretKey:     c.OBSSecretKey,
		Bucket:        c.OBSBucket,
		Region:        c.OBSRegion,
		EncryptionKey: c.OBSEncryptionKey,
	}
}

// GetAuditConfig 获取审计配置
func (c *SystemConfig) GetAuditConfig() *SystemAuditConfig {
	return &SystemAuditConfig{
		RetentionDays:   c.AuditRetentionDays,
		ArchiveInterval: c.AuditArchiveInterval,
	}
}

// GetBasicConfig 获取基本系统配置
func (c *SystemConfig) GetBasicConfig() *SystemBasicConfig {
	return &SystemBasicConfig{
		SystemName:   c.SystemName,
		SystemLogo:   c.SystemLogo,
		ContactEmail: c.SystemContactEmail,
		Version:      c.SystemVersion,
		DebugMode:    c.SystemDebugMode,
	}
}

// UpdateFromOIDCConfig 从OIDC配置更新
func (c *SystemConfig) UpdateFromOIDCConfig(config *SystemOIDCConfig) {
	c.OIDCEnabled = config.Enabled
	c.OIDCIssuerURL = config.IssuerURL
	c.OIDCClientID = config.ClientID
	c.OIDCClientSecret = config.ClientSecret
	c.OIDCRedirectURI = config.RedirectURI
	c.OIDCScopes = config.Scopes
	c.OIDCGroupsClaim = config.GroupsClaim
}

// UpdateFromFeishuConfig 从飞书配置更新
func (c *SystemConfig) UpdateFromFeishuConfig(config *SystemFeishuConfig) {
	c.FeishuAppID = config.AppID
	c.FeishuAppSecret = config.AppSecret
}

// UpdateFromOBSConfig 从OBS配置更新
func (c *SystemConfig) UpdateFromOBSConfig(config *SystemOBSConfig) {
	c.OBSEnabled = config.Enabled
	c.OBSEndpoint = config.Endpoint
	c.OBSAccessKey = config.AccessKey
	c.OBSSecretKey = config.SecretKey
	c.OBSBucket = config.Bucket
	c.OBSRegion = config.Region
	c.OBSEncryptionKey = config.EncryptionKey
}

// UpdateFromAuditConfig 从审计配置更新
func (c *SystemConfig) UpdateFromAuditConfig(config *SystemAuditConfig) {
	c.AuditRetentionDays = config.RetentionDays
	c.AuditArchiveInterval = config.ArchiveInterval
}

// UpdateFromBasicConfig 从基本系统配置更新
func (c *SystemConfig) UpdateFromBasicConfig(config *SystemBasicConfig) {
	c.SystemName = config.SystemName
	c.SystemLogo = config.SystemLogo
	c.SystemContactEmail = config.ContactEmail
	c.SystemVersion = config.Version
	c.SystemDebugMode = config.DebugMode
}

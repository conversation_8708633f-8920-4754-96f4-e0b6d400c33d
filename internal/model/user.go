package model

import (
	"time"

	"gorm.io/gorm"
)

// UserStatus 用户状态枚举
type UserStatus int8

const (
	UserStatusInactive UserStatus = 0 // 未激活
	UserStatusActive   UserStatus = 1 // 激活
	UserStatusLocked   UserStatus = 2 // 锁定
	UserStatusDeleted  UserStatus = 3 // 已删除
)

// IdentityProvider 身份提供者常量
const (
	IdentityProviderLocal  = "local"  // 本地认证
	IdentityProviderOIDC   = "oidc"   // OIDC认证
	IdentityProviderFeishu = "feishu" // 飞书认证
)

// User 用户模型 - 按照technical-design.md 4.3.1节设计
type User struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Username  string         `gorm:"size:50;uniqueIndex;not null" json:"username"`        // 用户名，唯一标识
	Password  string         `gorm:"size:100" json:"-"`                                   // 密码哈希，OIDC用户可为空
	Email     string         `gorm:"size:100;uniqueIndex" json:"email"`                   // 邮箱地址，唯一
	Nickname  string         `gorm:"size:50" json:"nickname"`                             // 昵称/显示名称
	Avatar    string         `gorm:"size:255" json:"avatar"`                              // 头像URL
	Phone     string         `gorm:"size:20" json:"phone"`                                // 手机号码
	Status    int            `gorm:"default:1" json:"status"`                             // 用户状态：0=禁用，1=启用
	LastLogin *time.Time     `json:"last_login"`                                          // 最后登录时间
	CreatedAt time.Time      `json:"created_at"`                                          // 创建时间
	UpdatedAt time.Time      `json:"updated_at"`                                          // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`                                      // 软删除时间

	// OIDC相关信息
	OIDCSubject      string `gorm:"column:oidc_subject;size:255;uniqueIndex" json:"oidc_subject"`     // OIDC用户唯一标识
	IdentityProvider string `gorm:"size:50" json:"identity_provider"`                                 // 身份提供商：local, keycloak, feishu

	// 飞书集成字段
	FeishuOpenID  string `gorm:"size:100;index" json:"feishu_open_id"`  // 飞书OpenID，用于飞书API调用
	FeishuUnionID string `gorm:"size:100;index" json:"feishu_union_id"` // 飞书UnionID，企业内用户唯一标识
	FeishuUserID  string `gorm:"size:100;index" json:"feishu_user_id"`  // 飞书UserID，用于飞书内部标识

	// 关联关系
	UserGroups []UserGroup `gorm:"many2many:user_group_members;" json:"user_groups,omitempty"` // 用户所属的组
	Projects   []Project   `gorm:"many2many:project_members;" json:"projects,omitempty"`       // 用户参与的项目
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password"`
	Email    string `json:"email" binding:"required,email"`
	Name     string `json:"name" binding:"required"`
	Phone    string `json:"phone"`
	Avatar   string `json:"avatar"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Email  string     `json:"email" binding:"omitempty,email"`
	Name   string     `json:"name"`
	Phone  string     `json:"phone"`
	Avatar string     `json:"avatar"`
	Status UserStatus `json:"status"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID               uint       `json:"id"`
	Username         string     `json:"username"`
	Email            string     `json:"email"`
	Name             string     `json:"name"`
	Avatar           string     `json:"avatar"`
	Phone            string     `json:"phone"`
	Status           UserStatus `json:"status"`
	LastLoginAt      *time.Time `json:"last_login_at"`
	IdentityProvider string     `json:"identity_provider"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// OIDCUserInfo OIDC用户信息
type OIDCUserInfo struct {
	Subject           string `json:"sub"`
	Email             string `json:"email"`
	Name              string `json:"name"`
	PreferredUsername string `json:"preferred_username"`
	Picture           string `json:"picture"`
	FeishuOpenID      string `json:"feishu_open_id"`
	FeishuUnionID     string `json:"feishu_union_id"`
	FeishuUserID      string `json:"feishu_user_id"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

package router

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/service"
)

// RouteInfo 路由信息结构体，记录API路由信息
type RouteInfo struct {
	Method          string // HTTP方法
	Path            string // 路由路径
	Resource        string // 资源标识
	Action          string // 操作类型
	ResourceName    string // 资源友好名称（用于显示）
	ActionName      string // 操作友好名称（用于显示）
	ResourceType    string // 资源类型，默认为api
	Description     string // 描述信息
	SkipPermission  bool   // 是否跳过权限注册
}

// RouteOption 路由选项函数
type RouteOption func(*RouteInfo)

// WithResource 设置资源标识
func WithResource(resource string) RouteOption {
	return func(r *RouteInfo) {
		r.Resource = resource
	}
}

// WithAction 设置操作类型
func WithAction(action string) RouteOption {
	return func(r *RouteInfo) {
		r.Action = action
	}
}

// WithResourceName 设置资源友好名称
func WithResourceName(name string) RouteOption {
	return func(r *RouteInfo) {
		r.ResourceName = name
	}
}

// WithActionName 设置操作友好名称
func WithActionName(name string) RouteOption {
	return func(r *RouteInfo) {
		r.ActionName = name
	}
}

// WithDescription 设置描述信息
func WithDescription(desc string) RouteOption {
	return func(r *RouteInfo) {
		r.Description = desc
	}
}

// WithResourceType 设置资源类型
func WithResourceType(resourceType string) RouteOption {
	return func(r *RouteInfo) {
		r.ResourceType = resourceType
	}
}

// SkipPermissionRegistration 跳过权限注册
func SkipPermissionRegistration() RouteOption {
	return func(r *RouteInfo) {
		r.SkipPermission = true
	}
}

// RouteCollector 路由收集器，用于收集API路由并注册为资源
type RouteCollector struct {
	rbacService service.RBACService
	logger              *zap.Logger
	tracer              trace.Tracer
	routes              []RouteInfo
	// 用于映射HTTP方法到动词的映射
	methodToAction map[string]string
	// 路径参数匹配正则
	paramRegex *regexp.Regexp
}

// NewRouteCollector 创建新的路由收集器
func NewRouteCollector(rbacService service.RBACService, logger *zap.Logger, tracer trace.Tracer) *RouteCollector {
	return &RouteCollector{
		rbacService: rbacService,
		logger:      logger,
		tracer:      tracer,
		routes:      make([]RouteInfo, 0),
		methodToAction: map[string]string{
			"GET":    "list",
			"POST":   "create",
			"PUT":    "update",
			"PATCH":  "update",
			"DELETE": "delete",
		},
		paramRegex: regexp.MustCompile(`:[^/]+`),
	}
}

// AddRoute 添加路由信息
func (rc *RouteCollector) AddRoute(method, path, basePath string, options ...RouteOption) {
	// 完整路径，用于日志记录
	fullPath := path

	// 初始化RouteInfo
	routeInfo := RouteInfo{
		Method:       method,
		Path:         fullPath,
		ResourceType: model.ResourceTypeAPI, // 默认为API类型
	}

	// 应用选项
	for _, option := range options {
		option(&routeInfo)
	}

	// 如果未指定资源和操作，则自动解析
	if routeInfo.Resource == "" || routeInfo.Action == "" {
		// 如果basePath为空，则尝试从path中提取
		if basePath == "" {
			// 通常API路径是 /api/v1/...
			parts := strings.Split(strings.TrimPrefix(path, "/"), "/")
			if len(parts) >= 2 {
				// 提取前两个部分作为basePath（比如/api/v1）
				basePath = "/" + parts[0] + "/" + parts[1]
			}
		}

		// 自动解析资源和操作
		resource, action := rc.parseRouteToResourceAction(method, path, basePath)
		
		// 如果未在选项中指定，则使用自动解析的结果
		if routeInfo.Resource == "" {
			routeInfo.Resource = resource
		}
		if routeInfo.Action == "" {
			routeInfo.Action = action
		}
	}

	// 如果资源或操作仍为空，则忽略
	if routeInfo.Resource == "" || routeInfo.Action == "" {
		rc.logger.Debug("忽略无法解析的路由",
			zap.String("method", method),
			zap.String("path", path),
			zap.String("basePath", basePath))
		return
	}

	rc.logger.Debug("收集到路由",
		zap.String("method", method),
		zap.String("path", fullPath),
		zap.String("resource", routeInfo.Resource),
		zap.String("action", routeInfo.Action))

	// 添加到收集的路由列表
	rc.routes = append(rc.routes, routeInfo)
}

// 根据路由路径解析资源和操作
func (rc *RouteCollector) parseRouteToResourceAction(method, path, basePath string) (string, string) {
	// 移除API前缀和版本
	trimmedPath := path
	if basePath != "" {
		trimmedPath = strings.TrimPrefix(path, basePath)
	}

	// 移除URL参数
	if idx := strings.Index(trimmedPath, "?"); idx != -1 {
		trimmedPath = trimmedPath[:idx]
	}

	// 分割路径
	segments := strings.Split(strings.Trim(trimmedPath, "/"), "/")
	if len(segments) == 0 {
		return "", ""
	}

	// 规范化路径，将参数替换为通用标识符
	normalizedSegments := rc.normalizePath(segments)

	// 路径处理逻辑 - 处理不同的路由模式

	// 资源名通常是第一段
	resource := normalizedSegments[0]

	// 特殊情况：RBAC子路由如 /rbac/roles
	if resource == "rbac" && len(normalizedSegments) > 1 {
		resource = normalizedSegments[1]
	}

	// 特殊情况：系统配置子路由如 /system/config/basic
	if resource == "system" && len(normalizedSegments) > 2 {
		if normalizedSegments[1] == "config" {
			resource = "config-" + normalizedSegments[2]
		}
	}

	// 如果路径包含参数，则为单资源操作
	isDetailPath := false
	for _, segment := range segments {
		if strings.HasPrefix(segment, ":") {
			isDetailPath = true
			break
		}
	}

	// 特殊情况：嵌套资源如 /clusters/:id/namespaces
	if len(normalizedSegments) > 2 && normalizedSegments[1] == "id" {
		if len(normalizedSegments) > 2 {
			// 处理嵌套资源：如 /clusters/:id/namespaces
			resource = normalizedSegments[2]
		}
	}

	// 根据HTTP方法和路径确定操作
	var action string
	if val, exists := rc.methodToAction[method]; exists {
		action = val

		// 对于GET请求，根据路径确定是list还是read操作
		if method == "GET" {
			if isDetailPath {
				action = "read"
			} else {
				action = "list"
			}
		}
	} else {
		action = "exec" // 默认操作
	}

	return resource, action
}

// 规范化路径，将参数替换为通用标识符
func (rc *RouteCollector) normalizePath(segments []string) []string {
	if len(segments) == 0 {
		return segments
	}

	// 对于以冒号开头的参数，统一替换为 "id"
	normalized := make([]string, len(segments))
	for i, segment := range segments {
		if strings.HasPrefix(segment, ":") {
			normalized[i] = "id"
		} else {
			normalized[i] = segment
		}
	}
	return normalized
}

// RegisterResources 将收集的路由注册为系统资源
func (rc *RouteCollector) RegisterResources(ctx context.Context) error {
	ctx, span := rc.tracer.Start(ctx, "RouteCollector.RegisterResources")
	defer span.End()

	// 按资源分组 - 使用map保存所有路由信息
	resourceActionMap := make(map[string]map[string]RouteInfo)
	for _, route := range rc.routes {
		// 跳过标记为不注册权限的路由
		if route.SkipPermission {
			continue
		}
		
		// 初始化资源map
		if _, exists := resourceActionMap[route.Resource]; !exists {
			resourceActionMap[route.Resource] = make(map[string]RouteInfo)
		}
		// 保存路由信息
		resourceActionMap[route.Resource][route.Action] = route
	}

	span.SetAttributes(attribute.Int("collected_resources", len(resourceActionMap)))
	rc.logger.Info("收集到的API资源", zap.Int("数量", len(resourceActionMap)))

	// 资源名称映射表，提供更友好的显示名称
	resourceNameMap := map[string]string{
		"users":             "用户",
		"permissions":       "权限",
		"groups":            "用户组",
		"resources":         "资源",
		"clusters":          "集群",
		"namespaces":        "命名空间",
		"nodes":             "节点",
		"pods":              "容器组",
		"deployments":       "部署",
		"services":          "服务",
		"ingresses":         "入口",
		"configmaps":        "配置映射",
		"secrets":           "密钥",
		"persistentvolumes": "持久卷",
		"events":            "事件",
		"logs":              "日志",
		"audit":             "审计",
		"approvals":         "审批",
		"config":            "配置",
		"auth":              "认证",
		"oidc":              "OIDC认证",
		"id":                "资源ID",
		"rbac":              "权限管理",
		"system":            "系统",
		"user":              "用户",
		"role":              "角色",
		"permission":        "权限",
		"group":             "用户组",
		"resource":          "资源",
		"cluster":           "集群",
		"namespace":         "命名空间",
		"node":              "节点",
		"pod":               "容器组",
		"deployment":        "部署",
		"service":           "服务",
		"ingress":           "入口",
		"configmap":         "配置映射",
		"secret":            "密钥",
		"persistentvolume":  "持久卷",
		"event":             "事件",
		"log":               "日志",
		"approval":          "审批",
	}

	// 动作名称映射表
	actionNameMap := map[string]string{
		"list":   "列表",
		"create": "创建",
		"read":   "详情",
		"update": "更新",
		"delete": "删除",
		"exec":   "执行",
	}

	// 已创建的资源权限计数
	createdCount := 0
	existingCount := 0

	// 注册资源
	for resource, actions := range resourceActionMap {
		// 跳过一些特殊路径
		if resource == "ping" || resource == "swagger" || resource == "docs" {
			continue
		}

		// 为每个操作创建资源权限
		for action, routeInfo := range actions {
			// 获取资源友好名称 - 优先使用路由中指定的
			resourceName := routeInfo.ResourceName
			if resourceName == "" {
				// 如果路由中未指定，尝试从映射中获取
				if name, exists := resourceNameMap[resource]; exists {
					resourceName = name
				} else {
					// 默认使用资源标识
					resourceName = resource
				}
			}

			// 获取操作友好名称 - 优先使用路由中指定的
			actionName := routeInfo.ActionName
			if actionName == "" {
				// 如果路由中未指定，尝试从映射中获取
				if name, exists := actionNameMap[action]; exists {
					actionName = name
				} else {
					// 默认使用操作标识
					actionName = action
				}
			}

			// 确定资源类型
			resourceType := routeInfo.ResourceType
			if resourceType == "" {
				resourceType = model.ResourceTypeAPI
			}

			// 构建符合technical-design.md规范的资源路径
			// 格式：{resource_type}:{scope_path}
			var resourcePath string
			if strings.HasPrefix(resource, "system:") {
				// 已经是标准格式，直接使用
				resourcePath = resource
			} else {
				// 转换为标准格式：api:system:{resource}
				resourcePath = fmt.Sprintf("system:%s", resource)
			}

			// 构建资源权限
			resourcePermission := &model.ResourcePermission{
				Name:         fmt.Sprintf("%s%s", resourceName, actionName),
				ResourceType: string(resourceType),
				ResourcePath: resourcePath,
				Actions:      action,
				ScopeLevel:   "system",
				Description:  routeInfo.Description,
			}

			// 如果描述为空，使用默认描述
			if resourcePermission.Description == "" {
				resourcePermission.Description = fmt.Sprintf("%s相关%s操作", resourceName, actionName)
			}

			// 检查资源权限是否已存在 - 需要检查resource_path和actions的组合
			existingResourcePermission, err := rc.rbacService.GetPermissionByPathAndActions(ctx, resourcePermission.ResourcePath, resourcePermission.Actions)
			if err == nil && existingResourcePermission != nil {
				// 资源权限已存在，跳过
				existingCount++
				continue
			}

			// 创建资源权限
			_, err = rc.rbacService.CreatePermission(ctx, &model.PermissionCreateRequest{
				Name:         resourcePermission.Name,
				ResourceType: resourcePermission.ResourceType,
				ResourcePath: resourcePermission.ResourcePath,
				Actions:      resourcePermission.Actions,
				ScopeLevel:   resourcePermission.ScopeLevel,
				Description:  resourcePermission.Description,
			})
			if err != nil {
				rc.logger.Error("创建API资源权限失败", 
					zap.Error(err), 
					zap.String("resource", resource), 
					zap.String("action", action))
				span.SetStatus(codes.Error, err.Error())
				continue
			}

			createdCount++
			rc.logger.Debug("自动注册API资源权限成功", 
				zap.String("资源", resource), 
				zap.String("操作", action),
				zap.String("名称", resourcePermission.Name))
		}
	}

	rc.logger.Info("API资源权限注册完成",
		zap.Int("新创建", createdCount),
		zap.Int("已存在", existingCount),
		zap.Int("总资源数", len(resourceActionMap)))

	return nil
}

package bootstrap

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/casbin/casbin/v2"
	casbinmodel "github.com/casbin/casbin/v2/model"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// initRBAC 初始化RBAC权限控制系统
// 返回Casbin执行器、Keycloak用户组映射器和可能的错误
func initRBAC(ctx context.Context, repo repository.Repository, logger *zap.Logger, tracer trace.Tracer) (*casbin.SyncedEnforcer, error) {
	logger.Info("正在初始化RBAC权限系统...")

	// 步骤1: 获取数据库连接
	// 获取数据库连接，需要类型断言到具体实现
	type dbGetter interface {
		GetDB() (*gorm.DB, error)
	}

	dbRepo, ok := repo.(dbGetter)
	if !ok {
		return nil, fmt.Errorf("repository不支持GetDB方法")
	}
	db, err := dbRepo.GetDB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 步骤2: 创建Casbin适配器，用于将策略存储在数据库中
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin适配器失败: %w", err)
	}
	logger.Debug("Casbin数据库适配器创建成功")

	// 步骤3: 从数据库获取活动的RBAC模型配置
	// 若不存在则使用默认模型
	rbacModel, err := repo.RBAC().GetActiveRBACModel(ctx)
	if err != nil {
		logger.Warn("从数据库获取RBAC模型失败，将使用默认模型", zap.Error(err))
		// 创建默认模型
		rbacModel = model.DefaultRBACModel()
		// 尝试保存默认模型到数据库
		if err := repo.RBAC().CreateRBACModel(ctx, rbacModel); err != nil {
			logger.Warn("保存默认RBAC模型到数据库失败", zap.Error(err))
		} else {
			logger.Info("默认RBAC模型已保存到数据库")
		}
	}
	logger.Info("已加载RBAC模型", zap.Bool("is_default", rbacModel.ID == 0))

	// 步骤4: 创建Casbin内存模型
	m, err := casbinmodel.NewModelFromString(rbacModel.Content)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin模型失败: %w", err)
	}

	// 步骤5: 创建Casbin执行器
	enforcer, err := casbin.NewSyncedEnforcer(m, adapter)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin执行器失败: %w", err)
	}

	// 步骤5.1: 添加自定义函数 - 按照technical-design.md 3.3.1节要求
	enforcer.AddFunction("resourcePathMatch", ResourcePathMatchFunc)
	enforcer.AddFunction("actionMatch", ActionMatchFunc)

	// 步骤6: 从数据库加载策略
	if err := enforcer.LoadPolicy(); err != nil {
		return nil, fmt.Errorf("加载Casbin策略失败: %w", err)
	}
	logger.Info("Casbin策略已从数据库加载")

	// 步骤7: 初始化默认策略 - 按照technical-design.md 3.3.3节权限策略示例
	if err := initDefaultPolicies(ctx, enforcer, logger); err != nil {
		logger.Warn("初始化默认策略失败", zap.Error(err))
	}

	// 步骤8: 启用策略自动重载，确保权限变更实时生效
	enforcer.StartAutoLoadPolicy(60) // 每60秒自动重载策略
	logger.Debug("Casbin策略自动重载已启用，间隔60秒")

	logger.Info("RBAC权限系统初始化成功")
	return enforcer, nil
}

// initDefaultPolicies 初始化默认策略 - 按照technical-design.md 3.3.3节权限策略示例
func initDefaultPolicies(ctx context.Context, enforcer *casbin.SyncedEnforcer, logger *zap.Logger) error {
	// 检查是否已有策略
	policies, err := enforcer.GetPolicy()
	if err != nil {
		return fmt.Errorf("failed to get policies: %w", err)
	}
	if len(policies) > 0 {
		logger.Info("Casbin策略已存在，跳过初始化")
		return nil
	}

	logger.Info("初始化默认Casbin策略...")

	// 1. 添加admin用户组权限策略 (p) - 系统级完全权限，使用group:前缀
	adminGroupPolicies := [][]string{
		{"group:admin", "*", "*", "*"}, // admin用户组拥有所有资源的完全权限
	}

	for _, policy := range adminGroupPolicies {
		params := make([]interface{}, len(policy))
		for i, v := range policy {
			params[i] = v
		}
		_, err := enforcer.AddPolicy(params...)
		if err != nil {
			logger.Error("添加admin用户组策略失败", zap.Error(err))
		} else {
			logger.Info("添加admin用户组策略成功", zap.Strings("policy", policy))
		}
	}

	// 2. 添加用户组成员关系 (g) - admin用户属于admin用户组，使用group:前缀
	groupMemberships := [][]string{
		{"user:1", "group:admin"}, // admin用户(ID=1)属于admin用户组
	}

	for _, membership := range groupMemberships {
		params := make([]interface{}, len(membership))
		for i, v := range membership {
			params[i] = v
		}
		_, err := enforcer.AddGroupingPolicy(params...)
		if err != nil {
			logger.Error("添加用户组关系失败", zap.Error(err))
		} else {
			logger.Info("添加用户组关系成功", zap.Strings("membership", membership))
		}
	}

	// 保存策略
	err = enforcer.SavePolicy()
	if err != nil {
		return fmt.Errorf("保存默认策略失败: %w", err)
	}

	logger.Info("默认Casbin策略初始化成功")
	return nil
}

// ResourcePathMatchFunc 资源路径匹配函数 - 按照technical-design.md 3.3.1节设计
func ResourcePathMatchFunc(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return false, nil
	}

	requestPath := args[0].(string)
	policyPath := args[1].(string)

	// 如果策略路径是通配符，直接匹配
	if policyPath == "*" {
		return true, nil
	}

	// 精确匹配
	if requestPath == policyPath {
		return true, nil
	}

	// 通配符匹配
	// 将策略路径转换为正则表达式
	pattern := strings.ReplaceAll(policyPath, "*", ".*")
	pattern = "^" + pattern + "$"

	matched, err := regexp.MatchString(pattern, requestPath)
	if err != nil {
		return false, err
	}

	return matched, nil
}

// ActionMatchFunc 动作匹配函数 - 按照technical-design.md 3.3.1节设计
func ActionMatchFunc(args ...interface{}) (interface{}, error) {
	if len(args) != 2 {
		return false, nil
	}

	requestAction := args[0].(string)
	policyAction := args[1].(string)

	// 如果策略动作是通配符，直接匹配
	if policyAction == "*" {
		return true, nil
	}

	// 精确匹配
	if requestAction == policyAction {
		return true, nil
	}

	// 多个动作用逗号分隔
	actions := strings.Split(policyAction, ",")
	for _, action := range actions {
		if strings.TrimSpace(action) == requestAction {
			return true, nil
		}
	}

	return false, nil
}

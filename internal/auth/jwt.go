package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"kubeops/internal/config"
	redisManager "kubeops/internal/redis"
)

// JWTClaims JWT声明结构 - 按照technical-design.md设计
type JWTClaims struct {
	// 标准声明
	jwt.RegisteredClaims
	
	// 用户信息
	UserID      uint   `json:"user_id"`
	Username    string `json:"username"`
	Email       string `json:"email"`
	DisplayName string `json:"display_name"`
	
	// 权限信息
	Groups      []string `json:"groups"`      // 用户组列表
	Permissions []string `json:"permissions"` // 权限列表
	
	// 会话信息
	SessionID    string `json:"session_id"`
	LoginTime    int64  `json:"login_time"`
	LastActivity int64  `json:"last_activity"`
	
	// 设备信息
	DeviceID   string `json:"device_id,omitempty"`
	DeviceType string `json:"device_type,omitempty"`
	UserAgent  string `json:"user_agent,omitempty"`
	IPAddress  string `json:"ip_address,omitempty"`
	
	// 安全标识
	TokenType    string `json:"token_type"`    // access_token, refresh_token
	TokenVersion int    `json:"token_version"` // 用于强制失效所有旧token
}

// TokenPair 访问令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// JWTService JWT服务接口
type JWTService interface {
	// Token生成
	GenerateTokenPair(ctx context.Context, userInfo *UserInfo, deviceInfo *DeviceInfo) (*TokenPair, error)
	GenerateAccessToken(ctx context.Context, claims *JWTClaims) (string, error)
	GenerateRefreshToken(ctx context.Context, claims *JWTClaims) (string, error)
	
	// Token验证
	VerifyToken(ctx context.Context, tokenString string) (*JWTClaims, error)
	VerifyAccessToken(ctx context.Context, tokenString string) (*JWTClaims, error)
	VerifyRefreshToken(ctx context.Context, tokenString string) (*JWTClaims, error)
	
	// Token刷新
	RefreshToken(ctx context.Context, refreshToken string) (*TokenPair, error)
	
	// Token撤销
	RevokeToken(ctx context.Context, tokenString string, reason string) error
	RevokeUserTokens(ctx context.Context, userID uint, reason string) error
	
	// 黑名单检查
	IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error)
}

// UserInfo 用户信息
type UserInfo struct {
	UserID      uint
	Username    string
	Email       string
	DisplayName string
	Groups      []string // 用户组列表
	Permissions []string // 权限列表
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID   string
	DeviceType string
	UserAgent  string
	IPAddress  string
}

// JWTServiceImpl JWT服务实现
type JWTServiceImpl struct {
	config          *config.Config
	redisManager    redisManager.RedisManager
	blacklistService *JWTBlacklistService
	logger          *zap.Logger
}

// NewJWTService 创建JWT服务
func NewJWTService(cfg *config.Config, redisManager redisManager.RedisManager) JWTService {
	blacklistService := NewJWTBlacklistService(redisManager, cfg)

	service := &JWTServiceImpl{
		config:          cfg,
		redisManager:    redisManager,
		blacklistService: blacklistService,
		logger:          zap.L().Named("jwt"),
	}

	return service
}

// GenerateTokenPair 生成Token对
func (s *JWTServiceImpl) GenerateTokenPair(ctx context.Context, userInfo *UserInfo, deviceInfo *DeviceInfo) (*TokenPair, error) {
	now := time.Now()
	sessionID := generateSessionID()
	tokenVersion := s.getCurrentTokenVersion(userInfo.UserID)
	
	// 构建基础Claims
	baseClaims := &JWTClaims{
		UserID:       userInfo.UserID,
		Username:     userInfo.Username,
		Email:        userInfo.Email,
		DisplayName:  userInfo.DisplayName,
		Groups:       userInfo.Groups,
		Permissions:  userInfo.Permissions,
		SessionID:    sessionID,
		LoginTime:    now.Unix(),
		LastActivity: now.Unix(),
		TokenVersion: tokenVersion,
	}
	
	// 添加设备信息
	if deviceInfo != nil {
		baseClaims.DeviceID = deviceInfo.DeviceID
		baseClaims.DeviceType = deviceInfo.DeviceType
		baseClaims.UserAgent = deviceInfo.UserAgent
		baseClaims.IPAddress = deviceInfo.IPAddress
	}
	
	// 生成Access Token
	accessClaims := *baseClaims
	accessClaims.TokenType = "access_token"
	accessClaims.RegisteredClaims = jwt.RegisteredClaims{
		ID:        generateTokenID(),
		Subject:   fmt.Sprintf("%d", userInfo.UserID),
		Issuer:    s.config.Auth.Tokens.AccessToken.Issuer,
		Audience:  s.config.Auth.Tokens.AccessToken.Audience,
		IssuedAt:  jwt.NewNumericDate(now),
		ExpiresAt: jwt.NewNumericDate(now.Add(s.config.Auth.Tokens.AccessToken.ExpiresIn)),
		NotBefore: jwt.NewNumericDate(now),
	}
	
	accessToken, err := s.GenerateAccessToken(ctx, &accessClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}
	
	// 生成Refresh Token
	refreshClaims := *baseClaims
	refreshClaims.TokenType = "refresh_token"
	refreshClaims.RegisteredClaims = jwt.RegisteredClaims{
		ID:        generateTokenID(),
		Subject:   fmt.Sprintf("%d", userInfo.UserID),
		Issuer:    s.config.Auth.Tokens.RefreshToken.Issuer,
		Audience:  s.config.Auth.Tokens.RefreshToken.Audience,
		IssuedAt:  jwt.NewNumericDate(now),
		ExpiresAt: jwt.NewNumericDate(now.Add(s.config.Auth.Tokens.RefreshToken.ExpiresIn)),
		NotBefore: jwt.NewNumericDate(now),
	}
	
	refreshToken, err := s.GenerateRefreshToken(ctx, &refreshClaims)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}
	
	// 记录用户活跃状态
	if err := s.recordUserActivity(ctx, userInfo.UserID, sessionID, &accessClaims); err != nil {
		s.logger.Warn("Failed to record user activity", zap.Error(err))
	}
	
	return &TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(s.config.Auth.Tokens.AccessToken.ExpiresIn.Seconds()),
	}, nil
}

// GenerateAccessToken 生成访问令牌
func (s *JWTServiceImpl) GenerateAccessToken(ctx context.Context, claims *JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.Auth.JWTSecret))
}

// GenerateRefreshToken 生成刷新令牌
func (s *JWTServiceImpl) GenerateRefreshToken(ctx context.Context, claims *JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.config.Auth.JWTSecret))
}

// VerifyToken 验证Token
func (s *JWTServiceImpl) VerifyToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	// 解析Token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.Auth.JWTSecret), nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}
	
	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}
	
	// 检查黑名单
	blacklisted, err := s.IsTokenBlacklisted(ctx, claims.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to check blacklist: %w", err)
	}
	if blacklisted {
		return nil, fmt.Errorf("token has been revoked")
	}
	
	return claims, nil
}

// VerifyAccessToken 验证访问令牌
func (s *JWTServiceImpl) VerifyAccessToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	claims, err := s.VerifyToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}
	
	if claims.TokenType != "access_token" {
		return nil, fmt.Errorf("invalid token type: expected access_token, got %s", claims.TokenType)
	}
	
	return claims, nil
}

// VerifyRefreshToken 验证刷新令牌
func (s *JWTServiceImpl) VerifyRefreshToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	claims, err := s.VerifyToken(ctx, tokenString)
	if err != nil {
		return nil, err
	}

	if claims.TokenType != "refresh_token" {
		return nil, fmt.Errorf("invalid token type: expected refresh_token, got %s", claims.TokenType)
	}

	return claims, nil
}

// RefreshToken 刷新Token
func (s *JWTServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*TokenPair, error) {
	// 验证Refresh Token
	claims, err := s.VerifyRefreshToken(ctx, refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// 检查刷新窗口期
	if !s.isWithinRefreshWindow(claims) {
		return nil, fmt.Errorf("token refresh window expired")
	}

	// 检查会话状态
	sessionKey := fmt.Sprintf("auth:activity:user:%d:session:%s", claims.UserID, claims.SessionID)
	exists, err := s.redisManager.Exists(ctx, sessionKey)
	if err != nil || exists == 0 {
		return nil, fmt.Errorf("session not found or expired")
	}

	// 检查刷新频率限制
	if s.config.Auth.Refresh.RateLimit.Enabled {
		if err := s.checkRefreshRateLimit(ctx, claims.UserID); err != nil {
			return nil, fmt.Errorf("refresh rate limit exceeded: %w", err)
		}
	}

	// 生成新的Token对
	userInfo := &UserInfo{
		UserID:      claims.UserID,
		Username:    claims.Username,
		Email:       claims.Email,
		DisplayName: claims.DisplayName,
		Groups:      claims.Groups,
		Permissions: claims.Permissions,
	}

	deviceInfo := &DeviceInfo{
		DeviceID:   claims.DeviceID,
		DeviceType: claims.DeviceType,
		UserAgent:  claims.UserAgent,
		IPAddress:  claims.IPAddress,
	}

	newTokenPair, err := s.GenerateTokenPair(ctx, userInfo, deviceInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new token pair: %w", err)
	}

	// 可选：撤销旧的Refresh Token
	if s.config.Auth.Refresh.RevokeOldTokenOnRefresh {
		if err := s.RevokeToken(ctx, refreshToken, "token_refresh"); err != nil {
			s.logger.Warn("Failed to revoke old refresh token", zap.Error(err))
		}
	}

	// 更新会话活跃时间
	s.updateSessionActivity(ctx, claims.UserID, claims.SessionID)

	return newTokenPair, nil
}

// RevokeToken 撤销Token
func (s *JWTServiceImpl) RevokeToken(ctx context.Context, tokenString string, reason string) error {
	return s.blacklistService.BlacklistToken(ctx, tokenString, reason)
}

// RevokeUserTokens 撤销用户的所有Token
func (s *JWTServiceImpl) RevokeUserTokens(ctx context.Context, userID uint, reason string) error {
	return s.blacklistService.BlacklistUserTokens(ctx, userID, reason)
}

// IsTokenBlacklisted 检查Token是否在黑名单中
func (s *JWTServiceImpl) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	return s.blacklistService.IsTokenBlacklisted(ctx, tokenID)
}

// isWithinRefreshWindow 检查是否在刷新窗口期内
func (s *JWTServiceImpl) isWithinRefreshWindow(claims *JWTClaims) bool {
	if claims.ExpiresAt == nil {
		return false
	}

	now := time.Now()
	expiresAt := claims.ExpiresAt.Time
	issuedAt := claims.IssuedAt.Time

	// 计算Token的总有效期
	totalValidity := expiresAt.Sub(issuedAt)

	// 计算刷新窗口期
	refreshWindow := s.config.Auth.Refresh.RefreshWindow
	if refreshWindow > totalValidity {
		refreshWindow = totalValidity
	}

	// 检查当前时间是否在刷新窗口期内
	refreshStartTime := expiresAt.Add(-refreshWindow)
	return now.After(refreshStartTime) && now.Before(expiresAt)
}

// checkRefreshRateLimit 检查刷新频率限制
func (s *JWTServiceImpl) checkRefreshRateLimit(ctx context.Context, userID uint) error {
	if !s.config.Auth.Refresh.RateLimit.Enabled {
		return nil
	}

	key := fmt.Sprintf("auth:refresh_rate:user:%d", userID)
	windowSize := s.config.Auth.Refresh.RateLimit.WindowSize
	maxRequests := s.config.Auth.Refresh.RateLimit.MaxRequests

	// 使用滑动窗口限流
	now := time.Now().Unix()
	windowStart := now - int64(windowSize.Seconds())

	// 清理过期的记录
	s.redisManager.ZRemRangeByScore(ctx, key, fmt.Sprintf("-inf %d", windowStart))

	// 检查当前窗口内的请求数量
	count, err := s.redisManager.ZCard(ctx, key)
	if err != nil {
		return err
	}

	if count >= int64(maxRequests) {
		return fmt.Errorf("refresh rate limit exceeded: %d requests in %v", count, windowSize)
	}

	// 记录当前请求
	s.redisManager.ZAdd(ctx, key, redis.Z{
		Score:  float64(now),
		Member: fmt.Sprintf("%d", now),
	})

	// 设置过期时间
	s.redisManager.Expire(ctx, key, windowSize)

	return nil
}

// recordUserActivity 记录用户活跃状态
func (s *JWTServiceImpl) recordUserActivity(ctx context.Context, userID uint, sessionID string, claims *JWTClaims) error {
	sessionKey := fmt.Sprintf("auth:activity:user:%d:session:%s", userID, sessionID)

	activityData := map[string]interface{}{
		"user_id":       userID,
		"session_id":    sessionID,
		"token_id":      claims.ID,
		"login_time":    claims.LoginTime,
		"last_activity": claims.LastActivity,
		"device_id":     claims.DeviceID,
		"device_type":   claims.DeviceType,
		"ip_address":    claims.IPAddress,
		"user_agent":    claims.UserAgent,
	}

	// 存储会话信息
	for field, value := range activityData {
		if err := s.redisManager.HSet(ctx, sessionKey, field, value); err != nil {
			return err
		}
	}

	// 设置过期时间
	ttl := s.config.RedisModules.AuthSecurity.TTL.UserActivity
	return s.redisManager.Expire(ctx, sessionKey, ttl)
}

// updateSessionActivity 更新会话活跃时间
func (s *JWTServiceImpl) updateSessionActivity(ctx context.Context, userID uint, sessionID string) {
	sessionKey := fmt.Sprintf("auth:activity:user:%d:session:%s", userID, sessionID)
	s.redisManager.HSet(ctx, sessionKey, "last_activity", time.Now().Unix())
}

// getCurrentTokenVersion 获取当前Token版本
func (s *JWTServiceImpl) getCurrentTokenVersion(userID uint) int {
	// 这里可以从数据库或Redis中获取用户的当前Token版本
	// 简化实现，返回固定值
	return 1
}

// 工具函数
func generateTokenID() string {
	return uuid.New().String()
}

func generateSessionID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Sprintf("session_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

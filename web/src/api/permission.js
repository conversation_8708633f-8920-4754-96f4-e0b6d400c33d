import request from '@/utils/request'

/**
 * 获取用户权限列表
 * @returns {Promise} 权限列表
 */
export function getUserPermissions() {
  return request({
    url: '/api/v1/rbac/current-user/permissions',
    method: 'get'
  })
}

/**
 * 检查单个权限
 * @param {String} resource - 资源
 * @param {String} action - 操作
 * @param {String} domain - 域，默认为system
 * @returns {Promise} 检查结果
 */
export function checkPermission(resource, action, domain = 'system') {
  return request({
    url: '/api/v1/rbac/check-permission',
    method: 'post',
    data: {
      resource,
      action,
      domain
    }
  })
}

/**
 * 批量检查权限
 * @param {Array} permissions - 权限列表，每项包含resource、action和domain
 * @returns {Promise} 检查结果
 */
export function batchCheckPermissions(permissions) {
  return request({
    url: '/api/v1/rbac/batch-check-permissions',
    method: 'post',
    data: {
      permissions
    }
  })
}

/**
 * 获取用户角色列表（实际是用户组列表）
 * @param {Number} userId - 用户ID，不传则获取当前用户
 * @returns {Promise} 角色列表
 */
export function getUserRoles(userId) {
  // 注意：当前系统使用用户组而非角色，这里返回用户组列表
  const url = userId ? `/api/v1/rbac/users/${userId}/effective-permissions` : '/api/v1/rbac/current-user/permissions'
  return request({
    url,
    method: 'get'
  })
}

/**
 * 获取用户组列表
 * @returns {Promise} 用户组列表
 */
export function getUserGroups() {
  return request({
    url: '/api/v1/usergroups',
    method: 'get'
  })
}

/**
 * 获取用户所在的用户组
 * @param {Number} userId - 用户ID，不传则获取当前用户
 * @returns {Promise} 用户组列表
 */
export function getUserInGroups(userId) {
  // 注意：当前系统没有直接获取用户所在组的API，这里返回用户权限信息
  const url = userId ? `/api/v1/rbac/users/${userId}/effective-permissions` : '/api/v1/rbac/current-user/permissions'
  return request({
    url,
    method: 'get'
  })
}

/**
 * 获取用户组的权限
 * @param {Number} groupId - 用户组ID
 * @returns {Promise} 权限列表
 */
export function getGroupPermissions(groupId) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'get'
  })
}

/**
 * 获取角色的权限
 * @param {Number} roleId - 角色ID
 * @returns {Promise} 权限列表
 */
export function getRolePermissions(roleId) {
  return request({
    url: `/api/v1/rbac/roles/${roleId}/permissions`,
    method: 'get'
  })
}

/**
 * 设置角色的权限
 * @param {Number} roleId - 角色ID
 * @param {Array} permissionIds - 权限ID列表
 * @returns {Promise} 设置结果
 */
export function setRolePermissions(roleId, permissionIds) {
  return request({
    url: `/api/v1/rbac/roles/${roleId}/permissions`,
    method: 'put',
    data: {
      permission_ids: permissionIds
    }
  })
}

/**
 * 设置用户的角色
 * @param {Number} userId - 用户ID
 * @param {Array} roleIds - 角色ID列表
 * @returns {Promise} 设置结果
 */
export function setUserRoles(userId, roleIds) {
  return request({
    url: `/api/v1/rbac/users/${userId}/roles`,
    method: 'put',
    data: {
      role_ids: roleIds
    }
  })
}

/**
 * 设置用户组的角色
 * @param {Number} groupId - 用户组ID
 * @param {Array} roleIds - 角色ID列表
 * @returns {Promise} 设置结果
 */
export function setGroupRoles(groupId, roleIds) {
  return request({
    url: `/api/v1/rbac/groups/${groupId}/roles`,
    method: 'put',
    data: {
      role_ids: roleIds
    }
  })
}

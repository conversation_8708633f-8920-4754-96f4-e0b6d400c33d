import request from '@/utils/request'

// 获取系统设置
export function getSystemSettings() {
  return request({
    url: '/api/v1/system/config/basic',
    method: 'get',
    timeout: 10000,
    validateStatus: function (status) {
      return status >= 200 && status < 500
    }
  })
}

// 更新系统设置
export function updateSystemSettings(data) {
  return request({
    url: '/api/v1/system/config/basic',
    method: 'put',
    data
  })
}

// 获取OIDC配置
export function getOIDCConfig() {
  return request({
    url: '/api/v1/system/config/oidc',
    method: 'get',
    // 添加错误处理和超时设置
    timeout: 10000,
    validateStatus: function (status) {
      return status >= 200 && status < 500 // 允许所有非500错误，以便在前端处理
    }
  })
}

// 更新OIDC配置
export function updateOIDCConfig(data) {
  return request({
    url: '/api/v1/system/config/oidc',
    method: 'put',
    data,
    timeout: 15000 // 增加超时时间，因为可能需要与OIDC服务器交互
  })
}

// 测试OIDC配置
export function testOIDCConfig(data) {
  return request({
    url: '/api/v1/system/config/oidc/test',
    method: 'post',
    data,
    timeout: 20000 // 测试连接可能需要更长时间
  })
}

// 获取OIDC状态
export function getOIDCStatus() {
  return request({
    url: '/api/v1/auth/oidc/status',
    method: 'get',
    timeout: 5000
  })
}

// 获取飞书配置
export function getFeishuConfig() {
  return request({
    url: '/api/v1/system/config/feishu',
    method: 'get',
    timeout: 10000,
    validateStatus: function (status) {
      return status >= 200 && status < 500
    }
  })
}

// 更新飞书配置
export function updateFeishuConfig(data) {
  return request({
    url: '/api/v1/system/config/feishu',
    method: 'put',
    data
  })
}

// 测试飞书配置
export function testFeishuConfig(data) {
  return request({
    url: '/api/v1/system/config/feishu/test',
    method: 'post',
    data,
    timeout: 15000 // 增加超时时间，因为可能需要与飞书服务器交互
  })
}

// 获取OBS配置
export function getOBSConfig() {
  return request({
    url: '/api/v1/system/config/obs',
    method: 'get',
    timeout: 10000,
    validateStatus: function (status) {
      return status >= 200 && status < 500
    }
  })
}

// 更新OBS配置
export function updateOBSConfig(data) {
  return request({
    url: '/api/v1/system/config/obs',
    method: 'put',
    data
  })
}

// 测试OBS配置
export function testOBSConfig(data) {
  return request({
    url: '/api/v1/system/config/obs/test',
    method: 'post',
    data,
    timeout: 20000 // 测试OBS连接可能需要较长时间
  })
}

// 获取审计配置
export function getAuditConfig() {
  return request({
    url: '/api/v1/system/config/audit',
    method: 'get',
    timeout: 10000,
    validateStatus: function (status) {
      return status >= 200 && status < 500
    }
  })
}

// 更新审计配置
export function updateAuditConfig(data) {
  return request({
    url: '/api/v1/system/config/audit',
    method: 'put',
    data
  })
}

// 测试邮件连接
export function testEmailConnection(data) {
  return request({
    url: '/api/v1/system/email/test',
    method: 'post',
    data,
    timeout: 15000 // 邮件服务测试可能需要较长时间
  })
}

// 获取系统状态
export function getSystemStatus() {
  return request({
    url: '/api/v1/system/status',
    method: 'get'
  })
}

// 获取系统版本信息
export function getSystemVersion() {
  return request({
    url: '/api/v1/system/version',
    method: 'get'
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/api/v1/system/upload',
    method: 'post',
    data,
    timeout: 60000, // 文件上传可能需要较长时间
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

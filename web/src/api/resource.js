import request from '@/utils/request'

// 获取资源列表（实际是权限列表）
export function fetchResourceList(query) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'get',
    params: query
  })
}

// 获取所有资源（不分页）
export function fetchAllResources(type) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'get',
    params: { type }
  })
}

// 获取资源详情（实际是权限详情）
export function getResourceDetail(id) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'get'
  })
}

// 创建资源（实际是创建权限）
export function createResource(data) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'post',
    data
  })
}

// 更新资源（实际是更新权限）
export function updateResource(id, data) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'put',
    data
  })
}

// 删除资源（实际是删除权限）
export function deleteResource(id) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'delete'
  })
}

// 获取资源操作列表（权限系统中暂不支持，返回空实现）
export function getResourceActions(id) {
  return Promise.resolve({ data: [] })
}

// 创建资源操作（权限系统中暂不支持，返回空实现）
export function createResourceAction(resourceId, data) {
  return Promise.resolve({ data: {} })
}

// 更新资源操作（权限系统中暂不支持，返回空实现）
export function updateResourceAction(resourceId, actionId, data) {
  return Promise.resolve({ data: {} })
}

// 删除资源操作（权限系统中暂不支持，返回空实现）
export function deleteResourceAction(resourceId, actionId) {
  return Promise.resolve({ data: {} })
}

// 初始化系统资源（权限系统中暂不支持，返回空实现）
export function initSystemResources() {
  return Promise.resolve({ data: {} })
}

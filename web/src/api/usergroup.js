import request from '@/utils/request'

/**
 * 用户组管理API
 * 适配后端 /api/v1/usergroups 接口
 */

// 获取用户组列表
export function fetchUserGroupList(query) {
  return request({
    url: '/api/v1/usergroups',
    method: 'get',
    params: query
  })
}

// 获取用户组详情
export function getUserGroupDetail(id) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'get'
  })
}

// 创建用户组
export function createUserGroup(data) {
  return request({
    url: '/api/v1/usergroups',
    method: 'post',
    data
  })
}

// 更新用户组
export function updateUserGroup(id, data) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'put',
    data
  })
}

// 删除用户组
export function deleteUserGroup(id) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'delete'
  })
}

// 获取用户组成员
export function getGroupMembers(id) {
  return request({
    url: `/api/v1/usergroups/${id}/members`,
    method: 'get'
  })
}

// 添加用户到用户组
export function addUserToGroup(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/members`,
    method: 'post',
    data
  })
}

// 从用户组移除用户
export function removeUserFromGroup(groupId, userId) {
  return request({
    url: `/api/v1/usergroups/${groupId}/members/${userId}`,
    method: 'delete'
  })
}

// 批量更新用户组成员
export function batchUpdateGroupMembers(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/members`,
    method: 'put',
    data
  })
}

// 获取用户组权限
export function getGroupPermissions(id) {
  return request({
    url: `/api/v1/usergroups/${id}/permissions`,
    method: 'get'
  })
}

// 分配用户组权限
export function assignGroupPermissions(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'post',
    data
  })
}

// 移除用户组权限
export function removeGroupPermissions(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'delete',
    data
  })
}

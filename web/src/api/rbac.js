import request from '@/utils/request'

/**
 * RBAC权限管理API
 * 适配后端 /api/v1/rbac 接口
 */

// 权限检查
export function checkPermission(data) {
  return request({
    url: '/api/v1/rbac/check-permission',
    method: 'post',
    data
  })
}

// 获取权限列表
export function getPermissionList(query) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'get',
    params: query
  })
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'post',
    data
  })
}

// 获取权限详情
export function getPermissionDetail(id) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'get'
  })
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'put',
    data
  })
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'delete'
  })
}

// 获取当前用户权限
export function getCurrentUserPermissions() {
  return request({
    url: '/api/v1/rbac/current-user/permissions',
    method: 'get'
  })
}

// 获取指定用户有效权限
export function getUserEffectivePermissions(userId) {
  return request({
    url: `/api/v1/rbac/users/${userId}/effective-permissions`,
    method: 'get'
  })
}

// 获取用户组权限
export function getGroupPermissions(groupId) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'get'
  })
}

// 分配用户组权限
export function assignGroupPermissions(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'post',
    data
  })
}

// 移除用户组权限
export function removeGroupPermissions(groupId, data) {
  return request({
    url: `/api/v1/usergroups/${groupId}/permissions`,
    method: 'delete',
    data
  })
}

// 获取用户直接权限
export function getUserDirectPermissions(userId) {
  return request({
    url: `/api/v1/users/${userId}/permissions`,
    method: 'get'
  })
}

// 分配用户权限
export function assignUserPermissions(userId, data) {
  return request({
    url: `/api/v1/users/${userId}/permissions`,
    method: 'post',
    data
  })
}

// 移除用户权限
export function removeUserPermissions(userId, data) {
  return request({
    url: `/api/v1/users/${userId}/permissions`,
    method: 'delete',
    data
  })
}

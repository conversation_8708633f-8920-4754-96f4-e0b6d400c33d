import request from '@/utils/request'

// 获取用户组列表
export function fetchGroupList(query) {
  return request({
    url: '/api/v1/usergroups',
    method: 'get',
    params: query
  })
}

// 获取所有用户组（不分页）
export function fetchAllGroups() {
  return request({
    url: '/api/v1/usergroups',
    method: 'get',
    params: { size: 1000 }
  })
}

// 获取用户组详情
export function getGroupDetail(id) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'get'
  })
}

// 创建用户组
export function createGroup(data) {
  return request({
    url: '/api/v1/usergroups',
    method: 'post',
    data
  })
}

// 更新用户组
export function updateGroup(id, data) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'put',
    data
  })
}

// 删除用户组
export function deleteGroup(id) {
  return request({
    url: `/api/v1/usergroups/${id}`,
    method: 'delete'
  })
}

// 获取用户组权限
export function getGroupPermissions(id) {
  return request({
    url: `/api/v1/usergroups/${id}/resource-permissions`,
    method: 'get'
  })
}

// 更新用户组权限
export function updateGroupPermissions(id, data) {
  return request({
    url: `/api/v1/usergroups/${id}/resource-permissions`,
    method: 'put',
    data
  })
}

// 获取用户组成员
export function getGroupMembers(id) {
  return request({
    url: `/api/v1/rbac/groups/${id}/members`,
    method: 'get'
  })
}

// 更新用户组成员
export function updateGroupMembers(id, data) {
  return request({
    url: `/api/v1/rbac/groups/${id}/members`,
    method: 'put',
    data
  })
}

// 获取所有权限
export function fetchAllPermissions(query = {}) {
  // 确保查询参数为数字
  const params = {
    page: parseInt(query.page) || 1,
    size: parseInt(query.size) || 10,
    keyword: query.keyword || '',
    type: query.type || ''
  }

  console.log('fetchAllPermissions请求URL:', '/api/v1/rbac/permissions')
  console.log('fetchAllPermissions请求参数(处理前):', query)
  console.log('fetchAllPermissions请求参数(处理后):', params)

  return request({
    url: '/api/v1/rbac/permissions',
    method: 'get',
    params: params
  })
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/api/v1/rbac/permissions',
    method: 'post',
    data
  })
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'put',
    data
  })
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/api/v1/rbac/permissions/${id}`,
    method: 'delete'
  })
}

// 获取用户权限（使用有效权限API）
export function getUserPermissions(id) {
  return request({
    url: `/api/v1/rbac/users/${id}/effective-permissions`,
    method: 'get'
  })
}

// 获取用户所属用户组（暂不支持，返回空实现）
export function getUserGroups(id) {
  return Promise.resolve({ data: [] })
}

// 更新用户所属用户组（暂不支持，返回空实现）
export function updateUserGroups(id, data) {
  return Promise.resolve({ data: {} })
}

// 添加用户到用户组
export function addUserToGroup(groupId, userId) {
  return request({
    url: `/api/v1/usergroups/${groupId}/members`,
    method: 'post',
    data: { userId }
  })
}

// 从用户组移除用户
export function removeUserFromGroup(groupId, userId) {
  return request({
    url: `/api/v1/usergroups/${groupId}/members/${userId}`,
    method: 'delete'
  })
}

// 检查权限
export function checkPermission(resource, action) {
  return request({
    url: '/api/v1/rbac/check-permission',
    method: 'post',
    data: { resource, action }
  })
}

// 获取部门列表
export function fetchDepartmentList() {
  return request({
    url: '/api/v1/rbac/departments',
    method: 'get'
  })
}

// 同步飞书部门
export function syncFeishuDepartments() {
  return request({
    url: '/api/v1/rbac/departments/sync/feishu',
    method: 'post'
  })
}

// 获取部门角色映射
export function getDepartmentRoleMapping() {
  return request({
    url: '/api/v1/rbac/departments/role-mapping',
    method: 'get'
  })
}

// 更新部门角色映射
export function updateDepartmentRoleMapping(data) {
  return request({
    url: '/api/v1/rbac/departments/role-mapping',
    method: 'put',
    data
  })
}

// 兼容性别名 - 为了向后兼容旧的API调用
export const fetchAllRoles = fetchGroupList
export const getRolePermissions = getGroupPermissions
export const updateRolePermissions = updateGroupPermissions

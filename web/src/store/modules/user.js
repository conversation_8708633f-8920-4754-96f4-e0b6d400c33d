import { login, logout, getInfo, getCurrentUserPermissions } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [], // Deprecated: Keep for compatibility, contains user group names
  userGroups: [], // User groups from backend (replaces roles)
  permissions: [] // User permissions
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
    setToken(token)
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USER_GROUPS: (state, userGroups) => {
    state.userGroups = userGroups
    // For backward compatibility, extract group names as roles
    // This allows existing code that checks roles to continue working
    state.roles = userGroups.map(group => group.name || group)
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        console.log('登录响应:', response)

        // 检查响应中是否包含data
        if (!response.data) {
          reject('登录响应异常')
          return
        }

        // 检查响应格式
        const responseData = response.data

        // 尝试从不同的路径获取token和过期时间
        let token = null
        let expiresAt = null

        if (responseData.data && responseData.data.access_token) {
          // 嵌套的data.data.access_token结构（后端实际返回格式）
          token = responseData.data.access_token
          expiresAt = responseData.data.expires_at
        } else if (responseData.data && responseData.data.token) {
          // 嵌套的data.data.token结构（兼容）
          token = responseData.data.token
          expiresAt = responseData.data.expires_at
        } else if (responseData.access_token) {
          // 直接的data.access_token结构
          token = responseData.access_token
          expiresAt = responseData.expires_at
        } else if (responseData.token) {
          // 直接的data.token结构（兼容）
          token = responseData.token
          expiresAt = responseData.expires_at
        } else {
          reject('无法获取登录令牌')
          return
        }

        if (!token) {
          reject('登录令牌为空')
          return
        }

        console.log('获取到token:', token.substr(0, 20) + '...')
        console.log('过期时间:', expiresAt)

        // 设置token到store和存储
        commit('SET_TOKEN', token)
        setToken(token, expiresAt)

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 处理OIDC登录
  handleOIDCLogin({ commit }, token) {
    return new Promise((resolve, reject) => {
      if (!token) {
        console.error('OIDC登录失败: token为空')
        reject('令牌为空')
        return
      }

      console.log('处理OIDC登录，设置token:', token)

      // 设置token到store和cookie
      commit('SET_TOKEN', token)
      setToken(token)

      // 验证token是否正确设置
      const storedToken = getToken()
      console.log('OIDC登录后从Cookie获取的token:', storedToken)

      if (storedToken !== token) {
        console.warn('OIDC登录: 存储的token与获取的不一致!')
      }

      resolve()
    })
  },

  // get user info
  getInfo({ commit, state, dispatch }) {
    console.log('开始获取用户信息，token:', state.token)
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        console.log('获取用户信息成功，响应:', response)
        // 正确处理嵌套的数据结构
        const { data } = response

        // 检查响应格式
        if (!data) {
          console.log('用户信息为空')
          reject('验证失败，请重新登录')
          return
        }

        // 提取最终的用户数据，考虑到返回格式可能是 {code: 20000, data: {...}}
        const userData = data.data || data
        console.log('解析后的用户数据:', userData)

        // 提取用户信息 - 适配后端返回的字段名（注意：后端已移除roles字段）
        const { groups, username, display_name, name, email, avatar, introduction } = userData
        console.log('提取用户组:', groups)

        // 使用用户组（后端已不再返回roles字段）
        if (groups && groups.length > 0) {
          // 处理用户组数据：如果是字符串数组，转换为对象数组
          const processedGroups = groups.map(group => {
            if (typeof group === 'string') {
              return { name: group, description: `${group}用户组` }
            }
            return group
          })
          commit('SET_USER_GROUPS', processedGroups)
        } else {
          console.log('用户组为空，设置默认用户组')
          commit('SET_USER_GROUPS', [{ name: 'user', description: '默认用户组' }])
        }

        // 设置用户基本信息，适配后端返回的字段
        commit('SET_NAME', name || display_name || username || '')
        commit('SET_AVATAR', avatar || 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif')
        commit('SET_INTRODUCTION', introduction || email || '')

        // 立即返回用户信息和角色，不等待权限获取
        const result = {
          ...userData,
          roles: state.roles // 使用store中的roles（实际是用户组名称）
        }

        console.log('getInfo返回结果:', result)
        resolve(result)

        // 异步获取用户权限，不阻塞路由生成
        dispatch('getUserPermissions').then(() => {
          console.log('用户权限获取成功')
        }).catch(error => {
          console.warn('获取用户权限失败:', error)
        })
      }).catch(error => {
        console.log('获取用户信息失败:', error)
        reject(error)
      })
    })
  },

  // 获取用户权限
  getUserPermissions({ commit }) {
    return new Promise((resolve, reject) => {
      getCurrentUserPermissions().then(response => {
        console.log('获取用户权限成功，响应:', response)
        const { data } = response

        if (data && data.data) {
          const permissions = data.data
          console.log('解析后的用户权限:', permissions)
          commit('SET_PERMISSIONS', permissions)
          resolve(permissions)
        } else {
          console.warn('权限数据格式异常:', data)
          commit('SET_PERMISSIONS', [])
          resolve([])
        }
      }).catch(error => {
        console.error('获取用户权限失败:', error)
        commit('SET_PERMISSIONS', [])
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      try {
        // 后端可能没有实现登出API，直接本地清除
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_USER_GROUPS', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        resetRouter()

        // 重置访问和缓存的视图
        dispatch('tagsView/delAllViews', null, { root: true })

        resolve()
      } catch (error) {
        reject(error)
      }
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_USER_GROUPS', [])
      commit('SET_PERMISSIONS', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

import { asyncRoutes, constantRoutes } from '@/router'
import { getUserPermissions } from '@/api/permission'
import { clearPermissionCache } from '@/utils/permission'

/**
 * 使用meta.roles和meta.permission确定当前用户是否有权限
 * @param roles
 * @param route
 * @param permissions
 */
function hasPermission(roles, route, permissions = []) {
  console.log('路由权限检查:', {
    path: route.path,
    roles: route.meta?.roles,
    permissions: route.meta?.permissions,
    permission: route.meta?.permission,
    userRoles: roles,
    userPermissions: permissions?.length || 0
  })

  // 如果没有设置任何权限要求，则允许访问
  if (!route.meta || (!route.meta.roles && !route.meta.permissions && !route.meta.permission)) {
    console.log('路由无权限要求，允许访问:', route.path)
    return true
  }

  // 检查角色权限（向后兼容）
  if (route.meta.roles && roles && roles.length > 0) {
    const hasRole = roles.some(role => route.meta.roles.includes(role))
    if (hasRole) {
      console.log('角色权限检查通过:', route.path, roles)
      return true
    }
  }

  // 检查权限数组（新的权限检查方式）
  if (route.meta.permissions && permissions && permissions.length > 0) {
    try {
      const { hasAnyPermission } = require('@/config/permissions')
      const hasPermissionResult = hasAnyPermission(permissions, route.meta.permissions)
      if (hasPermissionResult) {
        console.log('权限数组检查通过:', route.path)
        return true
      }
    } catch (error) {
      console.warn('权限数组检查失败:', error)
    }
  }

  // 检查单个权限（向后兼容）
  if (route.meta.permission && permissions && permissions.length > 0) {
    try {
      const { hasPermission: configHasPermission } = require('@/config/permissions')
      const hasPermissionResult = configHasPermission(permissions, route.meta.permission)
      if (hasPermissionResult) {
        console.log('单个权限检查通过:', route.path)
        return true
      }
    } catch (error) {
      console.warn('单个权限检查失败:', error)
    }
  }

  console.log('权限检查失败，拒绝访问:', route.path)
  return false
}

/**
 * 通过递归过滤异步路由表
 * @param routes asyncRoutes
 * @param roles
 * @param permissions
 */
export function filterAsyncRoutes(routes, roles, permissions = []) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp, permissions)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles, permissions)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: [],
  permissions: [], // 用户权限列表
  permissionLoaded: false // 权限是否已加载
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
    state.permissionLoaded = true
  },
  CLEAR_PERMISSIONS: (state) => {
    state.permissions = []
    state.permissionLoaded = false
    // 清除权限缓存
    clearPermissionCache()
  }
}

const actions = {
  generateRoutes({ commit, state }, roles) {
    return new Promise(resolve => {
      let accessedRoutes
      const permissions = state.permissions || []

      console.log('生成路由:', { roles, permissions: permissions.length })

      // 检查是否是admin用户（通过角色或权限）
      const isAdmin = roles.includes('admin') ||
                     permissions.some(p => p === 'api:system:*:*' || p === '*')

      if (isAdmin) {
        console.log('检测到admin权限，允许访问所有路由')
        accessedRoutes = asyncRoutes || []
      } else {
        console.log('非admin用户，过滤路由')
        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles, permissions)
      }

      console.log('生成的路由数量:', accessedRoutes.length)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },

  // 加载用户权限
  loadPermissions({ commit, rootState }) {
    return new Promise((resolve, reject) => {
      // 获取当前用户的权限
      getUserPermissions().then(response => {
        const { data } = response
        if (!data) {
          reject('获取权限失败，请重新登录')
          return
        }

        // 提取权限数据
        const permissions = data.data || data

        // 设置权限到permission store
        commit('SET_PERMISSIONS', permissions)

        // 同时设置到user store
        commit('user/SET_PERMISSIONS', permissions, { root: true })

        resolve(permissions)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 清除权限
  clearPermissions({ commit }) {
    commit('CLEAR_PERMISSIONS')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        placeholder="用户名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-refresh"
        @click="handleRefresh"
      >
        刷新
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column
        label="用户名"
        prop="username"
        align="center"
        width="150"
      >
        <template slot-scope="{row}">
          <span>{{ row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="会话ID"
        prop="session_id"
        align="center"
        width="200"
      >
        <template slot-scope="{row}">
          <span class="session-id">{{ row.session_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="设备类型"
        prop="device_type"
        align="center"
        width="120"
      >
        <template slot-scope="{row}">
          <el-tag :type="row.device_type === 'web' ? 'primary' : 'success'">
            {{ row.device_type }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="IP地址"
        prop="ip_address"
        align="center"
        width="150"
      >
        <template slot-scope="{row}">
          <span>{{ row.ip_address }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="用户代理"
        prop="user_agent"
        align="center"
        min-width="200"
      >
        <template slot-scope="{row}">
          <span class="user-agent">{{ row.user_agent }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="登录时间"
        prop="login_time"
        align="center"
        width="180"
      >
        <template slot-scope="{row}">
          <span>{{ row.login_time | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最后活动"
        prop="last_activity"
        align="center"
        width="180"
      >
        <template slot-scope="{row}">
          <span>{{ row.last_activity | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            size="mini"
            type="danger"
            @click="handleRevoke(row)"
          >
            撤销
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getUserSessions, revokeSession } from '@/api/user'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'SessionManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        username: undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getUserSessions(this.listQuery).then(response => {
        this.list = response.data.items || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleRefresh() {
      this.listQuery = {
        page: 1,
        limit: 20,
        username: undefined
      }
      this.getList()
    },
    handleRevoke(row) {
      this.$confirm('确定要撤销此会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        revokeSession(row.session_id).then(() => {
          this.$notify({
            title: '成功',
            message: '会话已撤销',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>

<style scoped>
.session-id {
  font-family: monospace;
  font-size: 12px;
}

.user-agent {
  font-size: 12px;
  color: #666;
}
</style>

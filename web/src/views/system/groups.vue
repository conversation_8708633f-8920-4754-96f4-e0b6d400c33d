<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="query.name" placeholder="用户组名称" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreateGroup">
        创建用户组
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="groupList"
      border
      style="width: 100%;"
      highlight-current-row
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户组名称" min-width="150px">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成员数量" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag>{{ row.member_count || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ formatTime(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdateGroup(row)">
            编辑
          </el-button>
          <el-button size="mini" type="success" @click="handleGroupMembers(row)">
            成员
          </el-button>
          <el-button size="mini" type="warning" @click="handleGroupPermissions(row)">
            权限
          </el-button>
          <el-button size="mini" type="danger" @click="handleDeleteGroup(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="query.page"
      :limit.sync="query.size"
      @pagination="fetchGroups"
    />

    <!-- 用户组表单对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建用户组' : '编辑用户组'" :visible.sync="dialogFormVisible">
      <el-form ref="groupForm" :rules="groupRules" :model="groupForm" label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="用户组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入用户组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="groupForm.description" type="textarea" :rows="3" placeholder="请输入用户组描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createGroup() : updateGroup()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 用户组成员对话框 -->
    <el-dialog title="管理用户组成员" :visible.sync="dialogMembersVisible" width="70%">
      <div v-if="currentGroup">
        <p class="dialog-info">用户组: {{ currentGroup.name }}</p>

        <el-transfer
          v-model="selectedMembers"
          :data="allUsers"
          :titles="['可用用户', '组成员']"
          :button-texts="['移除', '添加']"
          filterable
          filter-placeholder="搜索用户"
          :props="{
            key: 'id',
            label: 'name'
          }"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMembersVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateGroupMembers">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 用户组权限管理对话框 -->
    <el-dialog title="管理用户组权限" :visible.sync="dialogPermissionsVisible" width="70%">
      <div v-if="currentGroup">
        <p class="dialog-info">用户组: {{ currentGroup.name }}</p>

        <el-transfer
          v-model="selectedPermissions"
          :data="allPermissions"
          :titles="['可用权限', '已分配权限']"
          :button-texts="['移除', '添加']"
          filterable
          filter-placeholder="搜索权限"
          :props="{
            key: 'id',
            label: 'name'
          }"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogPermissionsVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateGroupPermissions">
          确认
        </el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { fetchGroupList, createGroup, updateGroup, deleteGroup, getGroupMembers, updateGroupMembers } from '@/api/role'
import { fetchUserList } from '@/api/user'
import { getGroupPermissions, assignGroupPermissions } from '@/api/rbac'
import { getPermissionList } from '@/api/rbac'
import Pagination from '@/components/Pagination'
import waves from '@/directive/waves'

export default {
  name: 'UserGroupManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      loading: false,
      total: 0,
      query: {
        page: 1,
        size: 10,
        name: undefined
      },
      groupList: [],
      dialogStatus: 'create',
      dialogFormVisible: false,
      groupForm: {
        id: undefined,
        name: '',
        description: ''
      },
      groupRules: {
        name: [{ required: true, message: '用户组名称不能为空', trigger: 'blur' }]
      },

      // 用户组成员管理
      currentGroup: null,
      dialogMembersVisible: false,
      allUsers: [],
      selectedMembers: [],

      // 用户组权限管理
      dialogPermissionsVisible: false,
      allPermissions: [],
      selectedPermissions: []

    }
  },
  created() {
    this.fetchGroups()
  },
  methods: {
    fetchGroups() {
      this.loading = true
      fetchGroupList(this.query).then(response => {
        this.groupList = response.data.items || response.data
        this.total = response.data.total || this.groupList.length
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleFilter() {
      this.query.page = 1
      this.fetchGroups()
    },
    resetForm() {
      this.groupForm = {
        id: undefined,
        name: '',
        description: ''
      }
    },
    handleCreateGroup() {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    createGroup() {
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          createGroup(this.groupForm).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '创建成功'
            })
            this.fetchGroups()
          })
        }
      })
    },
    handleUpdateGroup(row) {
      this.groupForm = Object.assign({}, row) // 使用对象的浅拷贝，避免影响原始数据
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['groupForm'].clearValidate()
      })
    },
    updateGroup() {
      this.$refs['groupForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.groupForm)
          updateGroup(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '更新成功'
            })
            this.fetchGroups()
          })
        }
      })
    },
    handleDeleteGroup(row) {
      this.$confirm('确认删除这个用户组吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGroup(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          // 删除后重新加载数据
          this.fetchGroups()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleGroupMembers(row) {
      this.currentGroup = row
      this.dialogMembersVisible = true

      // 获取所有用户
      fetchUserList().then(response => {
        this.allUsers = (response.data.items || response.data).map(user => ({
          id: user.id,
          name: `${user.username || user.name} ${user.email ? `(${user.email})` : ''}`
        }))
      })

      // 获取用户组成员
      getGroupMembers(row.id).then(response => {
        this.selectedMembers = response.data.map(user => user.id)
      })
    },
    updateGroupMembers() {
      if (!this.currentGroup) return

      updateGroupMembers(this.currentGroup.id, { userIds: this.selectedMembers }).then(() => {
        this.$message({
          type: 'success',
          message: '成员设置成功!'
        })
        this.dialogMembersVisible = false
        this.fetchGroups()
      })
    },
    handleGroupPermissions(row) {
      this.currentGroup = row
      this.dialogPermissionsVisible = true

      // 获取所有权限
      getPermissionList({ size: 1000 }).then(response => {
        this.allPermissions = (response.data.items || response.data || []).map(permission => ({
          id: permission.id,
          name: `${permission.resource_type}:${permission.resource_path}:${permission.action} (${permission.description || '无描述'})`
        }))
      }).catch(error => {
        console.error('获取权限列表失败:', error)
        this.$message.error('获取权限列表失败')
      })

      // 获取用户组权限
      getGroupPermissions(row.id).then(response => {
        this.selectedPermissions = (response.data || []).map(permission => permission.id)
      }).catch(error => {
        console.error('获取用户组权限失败:', error)
        this.selectedPermissions = []
      })
    },
    updateGroupPermissions() {
      if (!this.currentGroup) return

      assignGroupPermissions(this.currentGroup.id, {
        permission_ids: this.selectedPermissions
      }).then(() => {
        this.$message({
          type: 'success',
          message: '权限设置成功!'
        })
        this.dialogPermissionsVisible = false
      }).catch(error => {
        console.error('权限设置失败:', error)
        this.$message.error('权限设置失败: ' + (error.message || error))
      })
    },
    formatTime(time) {
      if (!time) return '-'

      try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
          return '-'
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('时间格式化失败:', error)
        return '-'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 15px;
}

.dialog-info {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.el-transfer {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}
</style>

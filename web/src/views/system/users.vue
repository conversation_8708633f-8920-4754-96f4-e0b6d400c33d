<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.username" placeholder="用户名" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.key" :label="item.display_name" :value="item.key" />
      </el-select>
      <el-select v-model="listQuery.source" placeholder="用户来源" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in sourceOptions" :key="item.key" :label="item.display_name" :value="item.key" />
      </el-select>
      <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        添加用户
      </el-button>
      <el-button class="filter-item" type="success" icon="el-icon-refresh" @click="handleSyncFeishu">
        同步飞书用户
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="显示名称" min-width="120px">
        <template slot-scope="{row}">
          <span>{{ row.display_name || row.nickname || row.name || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户类型" min-width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="getUserTypeTag(row.identity_provider)" size="small">
            <i :class="getUserTypeIcon(row.identity_provider)" style="margin-right: 4px;"></i>
            {{ getUserTypeName(row.identity_provider) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" min-width="150px">
        <template slot-scope="{row}">
          <span>{{ row.email || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 'active' ? 'success' : 'info'">
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ formatTime(row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="success" @click="handleGroups(row)">
            用户组
          </el-button>
          <el-button v-if="row.source === 'local'" size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
          <el-button v-if="row.status === 'active'" size="mini" type="warning" @click="handleStatus(row, 'inactive')">
            禁用
          </el-button>
          <el-button v-else size="mini" type="info" @click="handleStatus(row, 'active')">
            启用
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />

    <!-- 用户表单对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建用户' : '编辑用户'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" :disabled="dialogStatus === 'update'" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="temp.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="temp.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item v-if="dialogStatus === 'create'" label="密码" prop="password">
          <el-input v-model="temp.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="temp.status" class="filter-item" placeholder="请选择">
            <el-option v-for="item in statusOptions" :key="item.key" :label="item.display_name" :value="item.key" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 用户组对话框 -->
    <el-dialog title="管理用户组" :visible.sync="dialogGroupVisible">
      <div v-if="currentUser">
        <p class="dialog-info">用户: {{ currentUser.name }} ({{ currentUser.username }})</p>

        <el-transfer
          v-model="selectedGroups"
          :data="allGroups"
          :titles="['可用用户组', '已加入用户组']"
          :button-texts="['移除', '添加']"
          :props="{
            key: 'id',
            label: 'name'
          }"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogGroupVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateUserGroups">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 同步飞书用户对话框 -->
    <el-dialog title="同步飞书用户" :visible.sync="dialogSyncVisible">
      <div class="sync-options">
        <el-checkbox v-model="syncOptions.syncDepartments">同步部门结构</el-checkbox>
        <el-checkbox v-model="syncOptions.syncRoles">同步角色映射</el-checkbox>
        <el-checkbox v-model="syncOptions.overwriteExisting">覆盖已有用户信息</el-checkbox>
      </div>
      <div class="sync-warning">
        <i class="el-icon-warning"></i>
        <span>同步操作可能需要几分钟时间，期间请勿关闭页面</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogSyncVisible = false">
          取消
        </el-button>
        <el-button type="primary" :loading="syncLoading" @click="syncFeishuUsers">
          开始同步
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchUserList, createUser, updateUser, deleteUser, changeUserStatus, getUserGroups, updateUserGroups, syncFeishuUsers } from '@/api/user'
import { fetchAllGroups } from '@/api/role'
import Pagination from '@/components/Pagination'
import checkPermission from '@/utils/permission'
import waves from '@/directive/waves'

export default {
  name: 'UserManagement',
  components: { Pagination },
  directives: { wave: waves },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        size: 10,
        username: undefined,
        status: undefined,
        source: undefined
      },
      statusOptions: [
        { key: 'active', display_name: '启用' },
        { key: 'inactive', display_name: '禁用' }
      ],
      sourceOptions: [
        { key: 'local', display_name: '本地用户' },
        { key: 'feishu', display_name: '飞书用户' }
      ],
      temp: {
        id: undefined,
        username: '',
        name: '',
        email: '',
        password: '',
        status: 'active',
        source: 'local'
      },
      dialogFormVisible: false,
      dialogGroupVisible: false,
      dialogSyncVisible: false,
      dialogStatus: '',
      currentUser: null,
      allGroups: [],
      selectedGroups: [],
      syncOptions: {
        syncDepartments: true,
        syncRoles: true,
        overwriteExisting: false
      },
      syncLoading: false,
      rules: {
        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermission,
    getList() {
      this.listLoading = true
      fetchUserList(this.listQuery).then(response => {
        // 处理后端返回的数据结构：{users: [...], total: N} 或 {items: [...], total: N}
        this.list = response.data.users || response.data.items || response.data.data || []
        this.total = response.data.total || 0
        this.listLoading = false
        console.log('用户列表数据:', { list: this.list.length, total: this.total })
      }).catch(error => {
        console.error('获取用户列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        username: '',
        name: '',
        email: '',
        password: '',
        status: 'active',
        source: 'local'
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createUser(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '创建成功!'
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // 复制对象，避免修改原对象
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateUser(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '更新成功!'
            })
            this.getList()
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUser(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleStatus(row, status) {
      const statusText = status === 'active' ? '启用' : '禁用'
      this.$confirm(`确认${statusText}该用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        changeUserStatus(row.id, { status }).then(() => {
          this.$message({
            type: 'success',
            message: `${statusText}成功!`
          })
          // 直接更新本地数据，无需重新获取列表
          row.status = status
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${statusText}`
        })
      })
    },
    handleGroups(row) {
      this.currentUser = row
      this.dialogGroupVisible = true

      // 获取所有用户组
      fetchAllGroups().then(response => {
        this.allGroups = response.data

        // 获取用户当前所在的用户组
        getUserGroups(row.id).then(response => {
          this.selectedGroups = response.data.map(group => group.id)
        })
      })
    },
    updateUserGroups() {
      updateUserGroups(this.currentUser.id, { group_ids: this.selectedGroups }).then(() => {
        this.$message({
          type: 'success',
          message: '用户组更新成功!'
        })
        this.dialogGroupVisible = false
      })
    },
    handleSyncFeishu() {
      this.dialogSyncVisible = true
      this.syncOptions = {
        syncDepartments: true,
        syncRoles: true,
        overwriteExisting: false
      }
    },
    syncFeishuUsers() {
      this.syncLoading = true
      syncFeishuUsers(this.syncOptions).then(response => {
        this.$message({
          type: 'success',
          message: `同步成功! 新增${response.data.added}个用户，更新${response.data.updated}个用户`
        })
        this.syncLoading = false
        this.dialogSyncVisible = false
        this.getList()
      }).catch(() => {
        this.syncLoading = false
      })
    },
    formatTime(time) {
      if (!time) return '-'

      try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
          return '-'
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('时间格式化失败:', error)
        return '-'
      }
    },
    getUserTypeName(identityProvider) {
      const typeMap = {
        'local': '本地用户',
        'feishu': '飞书用户',
        'keycloak': 'Keycloak用户',
        'oidc': 'OIDC用户'
      }
      return typeMap[identityProvider] || '未知类型'
    },
    getUserTypeTag(identityProvider) {
      const tagMap = {
        'local': 'success',
        'feishu': 'warning',
        'keycloak': 'info',
        'oidc': 'primary'
      }
      return tagMap[identityProvider] || ''
    },
    getUserTypeIcon(identityProvider) {
      const iconMap = {
        'local': 'el-icon-user',
        'feishu': 'el-icon-chat-dot-round',
        'keycloak': 'el-icon-key',
        'oidc': 'el-icon-connection'
      }
      return iconMap[identityProvider] || 'el-icon-question'
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
}

.dialog-info {
  margin-bottom: 20px;
  font-weight: bold;
}

.sync-options {
  margin-bottom: 20px;

  .el-checkbox {
    display: block;
    margin-bottom: 10px;
  }
}

.sync-warning {
  padding: 10px;
  background-color: #FDF6EC;
  color: #E6A23C;
  border-radius: 4px;

  i {
    margin-right: 5px;
  }
}

.el-transfer {
  margin: 20px 0;
}
</style>

<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限分配</span>
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="用户权限分配" name="users">
          <div class="assignment-container">
            <div class="selector-container">
              <div class="selector-header">选择用户</div>
              <el-select v-model="selectedUser" placeholder="请选择用户" style="width: 100%" @change="handleUserChange">
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="`${item.username} (${item.display_name || item.nickname || item.name})`"
                  :value="item.id"
                />
              </el-select>

              <div v-if="selectedUser" class="permission-section">
                <div class="selector-header" style="margin-top: 20px">用户组权限</div>
                <div class="user-groups">
                  <el-tag v-for="group in userGroups" :key="group.id" type="success" style="margin-right: 10px;">
                    {{ group.name }}
                  </el-tag>
                  <span v-if="userGroups.length === 0" class="empty-tip">该用户未分配用户组</span>
                </div>

                <!-- 步骤1: 选择资源类型 -->
                <div class="selector-header" style="margin-top: 20px">步骤1: 选择资源类型</div>
                <el-radio-group v-model="userSelectedResourceType" @change="handleUserResourceTypeChange">
                  <el-radio label="api">API权限</el-radio>
                  <el-radio label="ui">UI权限</el-radio>
                  <el-radio label="k8s">K8s权限</el-radio>
                </el-radio-group>

                <!-- 步骤2: 根据资源类型显示权限选项 -->
                <div v-if="userSelectedResourceType" class="selector-header" style="margin-top: 20px">
                  步骤2: 选择{{ getResourceTypeName(userSelectedResourceType) }}权限
                </div>

                <!-- API权限选择 -->
                <div v-if="userSelectedResourceType === 'api'" class="permission-selection">
                  <el-transfer
                    v-model="userSelectedPermissions"
                    :data="userFilteredPermissions"
                    :titles="['可用API权限', '已分配API权限']"
                    :button-texts="['移除', '添加']"
                    filterable
                    filter-placeholder="搜索API权限"
                    :props="{
                      key: 'id',
                      label: 'name'
                    }"
                    style="text-align: left; display: inline-block"
                  />
                </div>

                <!-- UI权限选择 -->
                <div v-if="userSelectedResourceType === 'ui'" class="permission-selection">
                  <el-alert
                    title="UI权限说明"
                    description="UI权限控制用户在前端界面中可以看到和操作的功能模块"
                    type="info"
                    style="margin-bottom: 20px;"
                  />
                  <el-transfer
                    v-model="userSelectedPermissions"
                    :data="userFilteredPermissions"
                    :titles="['可用UI权限', '已分配UI权限']"
                    :button-texts="['移除', '添加']"
                    filterable
                    filter-placeholder="搜索UI权限"
                    :props="{
                      key: 'id',
                      label: 'name'
                    }"
                    style="text-align: left; display: inline-block"
                  />
                </div>

                <!-- K8s权限选择 -->
                <div v-if="userSelectedResourceType === 'k8s'" class="permission-selection">
                  <el-alert
                    title="K8s权限说明"
                    description="K8s权限支持集群级别、项目级别、应用级别的细粒度权限控制"
                    type="info"
                    style="margin-bottom: 20px;"
                  />

                  <div class="k8s-permission-levels">
                    <el-tabs v-model="userK8sPermissionLevel">
                      <el-tab-pane label="集群级别" name="cluster">
                        <el-transfer
                          v-model="userSelectedPermissions"
                          :data="getUserK8sPermissionsByLevel('cluster')"
                          :titles="['可用集群权限', '已分配集群权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索集群权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                      <el-tab-pane label="项目级别" name="project">
                        <el-transfer
                          v-model="userSelectedPermissions"
                          :data="getUserK8sPermissionsByLevel('project')"
                          :titles="['可用项目权限', '已分配项目权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索项目权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                      <el-tab-pane label="应用级别" name="application">
                        <el-transfer
                          v-model="userSelectedPermissions"
                          :data="getUserK8sPermissionsByLevel('application')"
                          :titles="['可用应用权限', '已分配应用权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索应用权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>

                <!-- 步骤3: 确认并保存 -->
                <div v-if="userSelectedResourceType" class="action-container" style="margin-top: 20px;">
                  <div class="selector-header">步骤3: 确认并保存权限分配</div>
                  <div style="margin: 10px 0;">
                    已选择 {{ userSelectedPermissions.length }} 个{{ getResourceTypeName(userSelectedResourceType) }}权限
                  </div>
                  <el-button type="primary" @click="saveUserPermissions">保存权限分配</el-button>
                  <el-button @click="resetUserPermissions">重置选择</el-button>
                </div>

                <!-- 当前有效权限展示 -->
                <div class="selector-header" style="margin-top: 30px">当前有效权限列表</div>
                <el-input
                  v-model="userPermissionFilter"
                  placeholder="搜索权限"
                  prefix-icon="el-icon-search"
                  style="margin-bottom: 10px;"
                />
                <div class="permission-list">
                  <el-tag
                    v-for="permission in filteredUserPermissions"
                    :key="permission"
                    size="small"
                    style="margin: 2px;"
                  >
                    {{ permission }}
                  </el-tag>
                  <div v-if="filteredUserPermissions.length === 0" class="empty-tip">
                    {{ userPermissions.length === 0 ? '该用户没有权限' : '没有匹配的权限' }}
                  </div>
                </div>
              </div>
              <div v-else class="empty-tip">请先选择一个用户</div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="用户组权限分配" name="groups">
          <div class="assignment-container">
            <div class="selector-container">
              <div class="selector-header">选择用户组</div>
              <el-select v-model="selectedGroup" placeholder="请选择用户组" style="width: 100%" @change="handleGroupChange">
                <el-option
                  v-for="item in groupList"
                  :key="item.id"
                  :label="`${item.name} (${item.description || '无描述'})`"
                  :value="item.id"
                />
              </el-select>

              <div v-if="selectedGroup" class="permission-section">
                <!-- 步骤1: 选择资源类型 -->
                <div class="selector-header" style="margin-top: 20px">步骤1: 选择资源类型</div>
                <el-radio-group v-model="selectedResourceType" @change="handleResourceTypeChange">
                  <el-radio label="api">API权限</el-radio>
                  <el-radio label="ui">UI权限</el-radio>
                  <el-radio label="k8s">K8s权限</el-radio>
                </el-radio-group>

                <!-- 步骤2: 根据资源类型显示权限选项 -->
                <div v-if="selectedResourceType" class="selector-header" style="margin-top: 20px">
                  步骤2: 选择{{ getResourceTypeName(selectedResourceType) }}权限
                </div>

                <!-- API权限选择 -->
                <div v-if="selectedResourceType === 'api'" class="permission-selection">
                  <el-transfer
                    v-model="selectedPermissions"
                    :data="filteredPermissions"
                    :titles="['可用API权限', '已分配API权限']"
                    :button-texts="['移除', '添加']"
                    filterable
                    filter-placeholder="搜索API权限"
                    :props="{
                      key: 'id',
                      label: 'name'
                    }"
                    style="text-align: left; display: inline-block"
                  />
                </div>

                <!-- UI权限选择 -->
                <div v-if="selectedResourceType === 'ui'" class="permission-selection">
                  <el-alert
                    title="UI权限说明"
                    description="UI权限控制用户在前端界面中可以看到和操作的功能模块"
                    type="info"
                    style="margin-bottom: 20px;"
                  />
                  <el-transfer
                    v-model="selectedPermissions"
                    :data="filteredPermissions"
                    :titles="['可用UI权限', '已分配UI权限']"
                    :button-texts="['移除', '添加']"
                    filterable
                    filter-placeholder="搜索UI权限"
                    :props="{
                      key: 'id',
                      label: 'name'
                    }"
                    style="text-align: left; display: inline-block"
                  />
                </div>

                <!-- K8s权限选择 -->
                <div v-if="selectedResourceType === 'k8s'" class="permission-selection">
                  <el-alert
                    title="K8s权限说明"
                    description="K8s权限支持集群级别、项目级别、应用级别的细粒度权限控制"
                    type="info"
                    style="margin-bottom: 20px;"
                  />

                  <div class="k8s-permission-levels">
                    <el-tabs v-model="k8sPermissionLevel">
                      <el-tab-pane label="集群级别" name="cluster">
                        <el-transfer
                          v-model="selectedPermissions"
                          :data="getK8sPermissionsByLevel('cluster')"
                          :titles="['可用集群权限', '已分配集群权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索集群权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                      <el-tab-pane label="项目级别" name="project">
                        <el-transfer
                          v-model="selectedPermissions"
                          :data="getK8sPermissionsByLevel('project')"
                          :titles="['可用项目权限', '已分配项目权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索项目权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                      <el-tab-pane label="应用级别" name="application">
                        <el-transfer
                          v-model="selectedPermissions"
                          :data="getK8sPermissionsByLevel('application')"
                          :titles="['可用应用权限', '已分配应用权限']"
                          :button-texts="['移除', '添加']"
                          filterable
                          filter-placeholder="搜索应用权限"
                          :props="{
                            key: 'id',
                            label: 'name'
                          }"
                          style="text-align: left; display: inline-block"
                        />
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>

                <!-- 步骤3: 确认并保存 -->
                <div v-if="selectedResourceType" class="action-container" style="margin-top: 20px;">
                  <div class="selector-header">步骤3: 确认并保存权限分配</div>
                  <div style="margin: 10px 0;">
                    已选择 {{ selectedPermissions.length }} 个{{ getResourceTypeName(selectedResourceType) }}权限
                  </div>
                  <el-button type="primary" @click="saveGroupPermissions">保存权限分配</el-button>
                  <el-button @click="resetGroupPermissions">重置选择</el-button>
                </div>
              </div>
              <div v-else class="empty-tip">请先选择一个用户组</div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { fetchUserList } from '@/api/user'
import { fetchGroupList } from '@/api/role'
import { getGroupPermissions, assignGroupPermissions, getUserDirectPermissions, assignUserPermissions } from '@/api/rbac'
import { getUserPermissions } from '@/api/role'
import { fetchAllPermissions } from '@/api/role'

export default {
  name: 'PermissionAssignment',
  data() {
    return {
      activeTab: 'users',

      // 用户权限分配
      userList: [],
      selectedUser: null,
      userGroups: [],
      userPermissions: [],
      userPermissionFilter: '',

      // 用户权限分配流程
      userSelectedResourceType: '',
      userK8sPermissionLevel: 'cluster',
      userSelectedPermissions: [],
      userOriginalPermissions: [],

      // 用户组权限分配
      groupList: [],
      selectedGroup: null,
      allPermissions: [],
      selectedPermissions: [],
      originalPermissions: [],

      // 权限分配流程
      selectedResourceType: '',
      k8sPermissionLevel: 'cluster'
    }
  },
  computed: {
    filteredUserPermissions() {
      if (!this.userPermissionFilter) {
        return this.userPermissions
      }
      return this.userPermissions.filter(permission =>
        permission.toLowerCase().includes(this.userPermissionFilter.toLowerCase())
      )
    },
    filteredPermissions() {
      if (!this.selectedResourceType) {
        return this.allPermissions
      }
      return this.allPermissions.filter(permission =>
        permission.name.startsWith(`${this.selectedResourceType}:`)
      )
    },
    userFilteredPermissions() {
      if (!this.userSelectedResourceType) {
        return this.allPermissions
      }
      return this.allPermissions.filter(permission =>
        permission.name.startsWith(`${this.userSelectedResourceType}:`)
      )
    }
  },
  created() {
    this.getUsers()
    this.getGroups()
    this.getAllPermissions()
  },
  methods: {
    getUsers() {
      fetchUserList({ size: 1000 }).then(response => {
        this.userList = response.data.users || response.data.items || response.data || []
      }).catch(error => {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      })
    },
    getGroups() {
      fetchGroupList({ size: 1000 }).then(response => {
        this.groupList = response.data.items || response.data || []
      }).catch(error => {
        console.error('获取用户组列表失败:', error)
        this.$message.error('获取用户组列表失败')
      })
    },
    getAllPermissions() {
      fetchAllPermissions({ size: 1000 }).then(response => {
        // 处理后端返回的数据结构：{permissions: [...], total: N} 或 {items: [...], total: N}
        const permissions = response.data.permissions || response.data.items || response.data || []
        this.allPermissions = permissions.map(permission => ({
          id: permission.id,
          name: `${permission.resource_type}:${permission.resource_path}:${permission.actions}`,
          description: permission.description
        }))
        console.log('获取到权限数量:', this.allPermissions.length)
      }).catch(error => {
        console.error('获取权限列表失败:', error)
        this.$message.error('获取权限列表失败')
      })
    },
    handleUserChange() {
      if (this.selectedUser) {
        this.getUserInfo()
        this.getUserPermissionList()
        this.getUserDirectPermissions()
      } else {
        this.userGroups = []
        this.userPermissions = []
        this.userSelectedResourceType = ''
        this.userSelectedPermissions = []
        this.userOriginalPermissions = []
      }
    },
    getUserInfo() {
      const user = this.userList.find(u => u.id === this.selectedUser)
      if (user && user.groups) {
        this.userGroups = user.groups
      } else {
        this.userGroups = []
      }
    },
    getUserPermissionList() {
      getUserPermissions(this.selectedUser).then(response => {
        this.userPermissions = response.data || []
      }).catch(error => {
        console.error('获取用户权限失败:', error)
        this.userPermissions = []
      })
    },
    handleGroupChange() {
      if (this.selectedGroup) {
        this.getGroupPermissions()
      } else {
        this.selectedPermissions = []
        this.originalPermissions = []
      }
    },
    getGroupPermissions() {
      getGroupPermissions(this.selectedGroup).then(response => {
        const permissions = response.data || []
        this.selectedPermissions = permissions.map(p => p.id)
        this.originalPermissions = [...this.selectedPermissions]
      }).catch(error => {
        console.error('获取用户组权限失败:', error)
        this.selectedPermissions = []
        this.originalPermissions = []
      })
    },
    saveGroupPermissions() {
      if (!this.selectedGroup) return

      assignGroupPermissions(this.selectedGroup, {
        permission_ids: this.selectedPermissions
      }).then(() => {
        this.$message({
          type: 'success',
          message: '权限设置成功!'
        })
        this.originalPermissions = [...this.selectedPermissions]
      }).catch(error => {
        console.error('权限设置失败:', error)
        this.$message.error('权限设置失败: ' + (error.message || error))
      })
    },
    resetGroupPermissions() {
      this.selectedPermissions = [...this.originalPermissions]
      this.selectedResourceType = ''
      this.k8sPermissionLevel = 'cluster'
    },
    getResourceTypeName(type) {
      const typeMap = {
        'api': 'API',
        'ui': 'UI',
        'k8s': 'K8s'
      }
      return typeMap[type] || type
    },
    handleResourceTypeChange() {
      // 当资源类型改变时，重置权限选择
      this.selectedPermissions = []
      this.k8sPermissionLevel = 'cluster'
    },
    getK8sPermissionsByLevel(level) {
      if (!this.selectedResourceType === 'k8s') {
        return []
      }

      // 根据级别过滤K8s权限
      return this.allPermissions.filter(permission => {
        const name = permission.name.toLowerCase()
        if (level === 'cluster') {
          return name.includes('k8s:cluster') || name.includes('k8s:node')
        } else if (level === 'project') {
          return name.includes('k8s:namespace') || name.includes('k8s:project')
        } else if (level === 'application') {
          return name.includes('k8s:deployment') || name.includes('k8s:service') ||
                 name.includes('k8s:pod') || name.includes('k8s:configmap')
        }
        return false
      })
    },
    getUserDirectPermissions() {
      // 获取用户直接分配的权限（不包括通过用户组获得的权限）
      getUserDirectPermissions(this.selectedUser).then(response => {
        const permissions = response.data || []
        this.userSelectedPermissions = permissions.map(p => p.id)
        this.userOriginalPermissions = [...this.userSelectedPermissions]
      }).catch(error => {
        console.error('获取用户直接权限失败:', error)
        this.userSelectedPermissions = []
        this.userOriginalPermissions = []
      })
    },
    handleUserResourceTypeChange() {
      // 当资源类型改变时，重置权限选择
      this.userSelectedPermissions = []
      this.userK8sPermissionLevel = 'cluster'
    },
    getUserK8sPermissionsByLevel(level) {
      if (!this.userSelectedResourceType === 'k8s') {
        return []
      }

      // 根据级别过滤K8s权限
      return this.allPermissions.filter(permission => {
        const name = permission.name.toLowerCase()
        if (level === 'cluster') {
          return name.includes('k8s:cluster') || name.includes('k8s:node')
        } else if (level === 'project') {
          return name.includes('k8s:namespace') || name.includes('k8s:project')
        } else if (level === 'application') {
          return name.includes('k8s:deployment') || name.includes('k8s:service') ||
                 name.includes('k8s:pod') || name.includes('k8s:configmap')
        }
        return false
      })
    },
    saveUserPermissions() {
      if (!this.selectedUser) return

      assignUserPermissions(this.selectedUser, {
        permission_ids: this.userSelectedPermissions
      }).then(() => {
        this.$message({
          type: 'success',
          message: '用户权限设置成功!'
        })

        this.userOriginalPermissions = [...this.userSelectedPermissions]

        // 重新获取用户权限列表
        this.getUserPermissionList()
      }).catch(error => {
        console.error('用户权限设置失败:', error)
        this.$message.error('用户权限设置失败: ' + (error.message || error))
      })
    },
    resetUserPermissions() {
      this.userSelectedPermissions = [...this.userOriginalPermissions]
      this.userSelectedResourceType = ''
      this.userK8sPermissionLevel = 'cluster'
    }
  }
}
</script>

<style scoped>
.assignment-container {
  padding: 20px;
}

.selector-container {
  max-width: 800px;
}

.selector-header {
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.permission-section {
  margin-top: 20px;
}

.user-groups {
  margin-bottom: 10px;
}

.permission-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.empty-tip {
  color: #909399;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.action-container {
  text-align: center;
}

.filter-item {
  margin-right: 10px;
}

.permission-selection {
  margin-top: 15px;
}

.k8s-permission-levels {
  margin-top: 15px;
}
</style>

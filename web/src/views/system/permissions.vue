<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限管理</span>
      </div>

      <div class="filter-container">
        <el-input v-model="permissionQuery.keyword" placeholder="权限名称/资源" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilterPermission" />

        <el-select v-model="permissionQuery.type" placeholder="权限类型" clearable class="filter-item" style="width: 120px;">
          <el-option label="系统" value="system" />
          <el-option label="资源" value="resource" />
          <el-option label="API" value="api" />
          <el-option label="UI" value="ui" />
        </el-select>

        <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilterPermission">
          搜索
        </el-button>

        <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreatePermission">
          创建权限
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="permissionList"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="资源类型" prop="resource_type" align="center" width="120">
          <template slot-scope="{row}">
            <el-tag :type="getPermissionTypeTag(row.resource_type)">
              {{ getPermissionTypeName(row.resource_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="资源路径" prop="resource_path" align="center" min-width="200" />
        <el-table-column label="操作" prop="actions" align="center" width="120" />
        <el-table-column label="描述" prop="description" align="center" min-width="200" />
        <el-table-column label="创建时间" prop="created_at" align="center" width="180">
          <template slot-scope="{row}">
            <span>{{ formatTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleEditPermission(row)">
              编辑
            </el-button>
            <el-button size="mini" type="danger" @click="handleDeletePermission(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="totalPermissions>0" :total="totalPermissions" :page.sync="permissionQuery.page" :limit.sync="permissionQuery.size" @pagination="getPermissions" />
    </el-card>

    <!-- 创建/编辑权限对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建权限' : '编辑权限'" :visible.sync="permissionDialogVisible">
      <el-form ref="permissionForm" :model="permissionForm" :rules="permissionRules" label-width="100px">
        <el-form-item label="资源类型" prop="resource_type">
          <el-select v-model="permissionForm.resource_type" placeholder="请选择资源类型">
            <el-option label="系统" value="system" />
            <el-option label="资源" value="resource" />
            <el-option label="API" value="api" />
            <el-option label="UI" value="ui" />
          </el-select>
        </el-form-item>
        <el-form-item label="资源路径" prop="resource_path">
          <el-input v-model="permissionForm.resource_path" placeholder="请输入资源路径" />
        </el-form-item>
        <el-form-item label="操作" prop="action">
          <el-input v-model="permissionForm.action" placeholder="请输入操作名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="permissionForm.description" type="textarea" placeholder="请输入权限描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createPermissionData() : updatePermissionData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAllPermissions, createPermission, updatePermission, deletePermission } from '@/api/role'
import checkPermission from '@/utils/permission'
import Pagination from '@/components/Pagination'
import waves from '@/directive/waves'

export default {
  name: 'PermissionManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      loading: false,
      permissionList: [],
      totalPermissions: 0,
      permissionQuery: {
        page: 1,
        size: 10,
        keyword: '',
        type: ''
      },
      permissionDialogVisible: false,
      dialogStatus: '',
      permissionForm: {
        resource_type: '',
        resource_path: '',
        action: '',
        description: ''
      },
      permissionRules: {
        resource_type: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
        resource_path: [{ required: true, message: '请输入资源路径', trigger: 'blur' }],
        action: [{ required: true, message: '请输入操作名称', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getPermissions()
  },
  methods: {
    checkPermission,
    getPermissions() {
      this.loading = true
      fetchAllPermissions(this.permissionQuery).then(response => {
        // 处理后端返回的数据结构：{permissions: [...], total: N} 或 {items: [...], total: N}
        this.permissionList = response.data.permissions || response.data.items || response.data || []
        this.totalPermissions = response.data.total || this.permissionList.length
        this.loading = false
        console.log('权限列表数据:', { list: this.permissionList.length, total: this.totalPermissions })
      }).catch(error => {
        console.error('获取权限列表失败:', error)
        this.permissionList = []
        this.totalPermissions = 0
        this.loading = false
      })
    },
    handleFilterPermission() {
      this.permissionQuery.page = 1
      this.getPermissions()
    },
    getPermissionTypeTag(type) {
      const typeMap = {
        'system': 'danger',
        'resource': 'success',
        'api': 'warning',
        'ui': 'info'
      }
      return typeMap[type] || ''
    },
    getPermissionTypeName(type) {
      const typeMap = {
        'system': '系统',
        'resource': '资源',
        'api': 'API',
        'ui': 'UI'
      }
      return typeMap[type] || type
    },
    handleCreatePermission() {
      this.resetPermissionForm()
      this.dialogStatus = 'create'
      this.permissionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['permissionForm'].clearValidate()
      })
    },
    handleEditPermission(row) {
      this.permissionForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.permissionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['permissionForm'].clearValidate()
      })
    },
    resetPermissionForm() {
      this.permissionForm = {
        resource_type: '',
        resource_path: '',
        action: '',
        description: ''
      }
    },
    createPermissionData() {
      this.$refs['permissionForm'].validate((valid) => {
        if (valid) {
          createPermission(this.permissionForm).then(() => {
            this.permissionDialogVisible = false
            this.$notify({
              title: '成功',
              message: '创建权限成功',
              type: 'success',
              duration: 2000
            })
            this.getPermissions()
          })
        }
      })
    },
    updatePermissionData() {
      this.$refs['permissionForm'].validate((valid) => {
        if (valid) {
          updatePermission(this.permissionForm.id, this.permissionForm).then(() => {
            this.permissionDialogVisible = false
            this.$notify({
              title: '成功',
              message: '更新权限成功',
              type: 'success',
              duration: 2000
            })
            this.getPermissions()
          })
        }
      })
    },
    handleDeletePermission(row) {
      this.$confirm('此操作将永久删除该权限, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePermission(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除权限成功',
            type: 'success',
            duration: 2000
          })
          this.getPermissions()
        })
      })
    },
    formatTime(time) {
      if (!time) return '-'

      try {
        const date = new Date(time)
        if (isNaN(date.getTime())) {
          return '-'
        }

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('时间格式化失败:', error)
        return '-'
      }
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}
</style>

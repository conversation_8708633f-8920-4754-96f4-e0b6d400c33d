import Layout from '@/layout'
import { UI_PERMISSIONS } from '@/config/permissions'

const authRouter = {
  path: '/auth',
  component: Layout,
  redirect: '/auth/users',
  alwaysShow: true,
  name: 'Authentication',
  meta: {
    title: '身份认证',
    icon: 'user',
    permissions: [UI_PERMISSIONS.USER_VIEW, UI_PERMISSIONS.USER_GROUP_VIEW, UI_PERMISSIONS.PERMISSION_VIEW]
  },
  children: [
    {
      path: 'users',
      component: () => import('@/views/system/users'),
      name: 'UserManagement',
      meta: {
        title: '用户管理',
        icon: 'user',
        permissions: [UI_PERMISSIONS.USER_VIEW]
      }
    },
    {
      path: 'user-groups',
      component: () => import('@/views/system/groups'),
      name: 'UserGroupManagement',
      meta: {
        title: '用户组管理',
        icon: 'peoples',
        permissions: [UI_PERMISSIONS.USER_GROUP_VIEW]
      }
    },
    {
      path: 'permissions',
      component: { render: (h) => h('router-view') },
      redirect: '/auth/permissions/list',
      alwaysShow: true,
      name: 'PermissionManagement',
      meta: {
        title: '权限管理',
        icon: 'key',
        permissions: [UI_PERMISSIONS.PERMISSION_VIEW]
      },
      children: [
        {
          path: 'list',
          component: () => import('@/views/system/permissions'),
          name: 'PermissionList',
          meta: {
            title: '权限列表',
            icon: 'list',
            permissions: [UI_PERMISSIONS.PERMISSION_VIEW]
          }
        },
        {
          path: 'check',
          component: () => import('@/views/system/permission-check'),
          name: 'PermissionCheck',
          meta: {
            title: '权限检查',
            icon: 'search',
            permissions: [UI_PERMISSIONS.PERMISSION_CHECK]
          }
        },
        {
          path: 'assignment',
          component: () => import('@/views/system/permission-assignment'),
          name: 'PermissionAssignment',
          meta: {
            title: '权限分配',
            icon: 'user',
            permissions: [UI_PERMISSIONS.PERMISSION_VIEW]
          }
        }
      ]
    },
    {
      path: 'sessions',
      component: () => import('@/views/system/sessions'),
      name: 'SessionManagement',
      meta: {
        title: '会话管理',
        icon: 'connection',
        permissions: [UI_PERMISSIONS.AUTH_VIEW]
      }
    }
  ]
}

export default authRouter

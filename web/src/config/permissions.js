/**
 * Frontend Permission Configuration
 *
 * This file maps backend resource permissions to frontend UI elements
 * and provides a centralized configuration for permission management.
 */

// Permission resource types that match the backend
export const RESOURCE_TYPES = {
  UI: 'ui',
  API: 'api',
  K8S: 'k8s'
}

// Permission actions that match the backend
export const ACTIONS = {
  VIEW: 'view',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage',
  LIST: 'list',
  GET: 'get'
}

// UI Resource permissions mapping - 基于后端实际API结构
export const UI_PERMISSIONS = {
  // 系统管理
  SYSTEM_MANAGE: 'api:system:*',

  // 用户管理
  USER_VIEW: 'api:system:users',
  USER_CREATE: 'api:system:users',
  USER_UPDATE: 'api:system:users',
  USER_DELETE: 'api:system:users',
  USER_MANAGE: 'api:system:users',

  // 用户组管理
  USER_GROUP_VIEW: 'api:system:user-group',
  USER_GROUP_CREATE: 'api:system:user-group',
  USER_GROUP_UPDATE: 'api:system:user-group',
  USER_GROUP_DELETE: 'api:system:user-group',
  USER_GROUP_MANAGE: 'api:system:user-group',

  // 权限管理
  PERMISSION_VIEW: 'api:system:permission',
  PERMISSION_CREATE: 'api:system:permission',
  PERMISSION_UPDATE: 'api:system:permission',
  PERMISSION_DELETE: 'api:system:permission',
  PERMISSION_MANAGE: 'api:system:permission',

  // 权限检查
  PERMISSION_CHECK: 'api:system:permission-check',

  // 集群管理
  CLUSTER_VIEW: 'api:system:cluster',
  CLUSTER_CREATE: 'api:system:cluster',
  CLUSTER_UPDATE: 'api:system:cluster',
  CLUSTER_DELETE: 'api:system:cluster',
  CLUSTER_MANAGE: 'api:system:cluster',

  // 审计日志
  AUDIT_LOG_VIEW: 'api:system:audit-log',
  AUDIT_ARCHIVE_VIEW: 'api:system:audit-archive',

  // 系统配置
  CONFIG_BASIC_VIEW: 'api:system:config-basic',
  CONFIG_BASIC_UPDATE: 'api:system:config-basic',
  CONFIG_AUDIT_VIEW: 'api:system:config-audit',
  CONFIG_AUDIT_UPDATE: 'api:system:config-audit',
  CONFIG_OIDC_VIEW: 'api:system:config-oidc',
  CONFIG_OIDC_UPDATE: 'api:system:config-oidc',
  CONFIG_OIDC_TEST: 'api:system:config-oidc',
  CONFIG_KEYCLOAK_VIEW: 'api:system:config-keycloak',
  CONFIG_KEYCLOAK_UPDATE: 'api:system:config-keycloak',
  CONFIG_FEISHU_VIEW: 'api:system:config-feishu',
  CONFIG_FEISHU_UPDATE: 'api:system:config-feishu',
  CONFIG_OBS_VIEW: 'api:system:config-obs',
  CONFIG_OBS_UPDATE: 'api:system:config-obs',

  // 认证相关
  AUTH_VIEW: 'api:system:auth',
  OIDC_VIEW: 'api:system:oidc'
}

// API Resource permissions mapping
export const API_PERMISSIONS = {
  // User API
  USER_READ: 'api:user:read',
  USER_WRITE: 'api:user:write',
  USER_DELETE: 'api:user:delete',

  // User group API
  USER_GROUP_READ: 'api:user-group:read',
  USER_GROUP_WRITE: 'api:user-group:write',
  USER_GROUP_DELETE: 'api:user-group:delete',

  // Cluster API
  CLUSTER_READ: 'api:cluster:read',
  CLUSTER_WRITE: 'api:cluster:write',
  CLUSTER_DELETE: 'api:cluster:delete',

  // Audit API
  AUDIT_READ: 'api:audit-log:read',
  AUDIT_WRITE: 'api:audit-log:write'
}

// K8s Resource permissions mapping
export const K8S_PERMISSIONS = {
  // Pod management
  PODS_LIST: 'k8s:pods:list',
  PODS_GET: 'k8s:pods:get',
  PODS_CREATE: 'k8s:pods:create',
  PODS_UPDATE: 'k8s:pods:update',
  PODS_DELETE: 'k8s:pods:delete',

  // Service management
  SERVICES_LIST: 'k8s:services:list',
  SERVICES_GET: 'k8s:services:get',
  SERVICES_CREATE: 'k8s:services:create',
  SERVICES_UPDATE: 'k8s:services:update',
  SERVICES_DELETE: 'k8s:services:delete',

  // Deployment management
  DEPLOYMENTS_LIST: 'k8s:deployments:list',
  DEPLOYMENTS_GET: 'k8s:deployments:get',
  DEPLOYMENTS_CREATE: 'k8s:deployments:create',
  DEPLOYMENTS_UPDATE: 'k8s:deployments:update',
  DEPLOYMENTS_DELETE: 'k8s:deployments:delete'
}

// User group names that match the backend
export const USER_GROUPS = {
  ADMIN: 'admin',
  USER_ADMIN: 'user-admin',
  SYSTEM_ADMIN: 'system-admin',
  K8S_ADMIN: 'k8s-admin',
  K8S_USER: 'k8s-user',
  AUDITOR: 'auditor',
  USER: 'user'
}

// Permission sets for different user groups
export const GROUP_PERMISSIONS = {
  [USER_GROUPS.ADMIN]: [
    // Admin has all permissions
    ...Object.values(UI_PERMISSIONS),
    ...Object.values(API_PERMISSIONS),
    ...Object.values(K8S_PERMISSIONS)
  ],

  [USER_GROUPS.USER_ADMIN]: [
    // User admin can manage users and user groups
    UI_PERMISSIONS.USER_VIEW,
    UI_PERMISSIONS.USER_CREATE,
    UI_PERMISSIONS.USER_UPDATE,
    UI_PERMISSIONS.USER_DELETE,
    UI_PERMISSIONS.USER_GROUP_VIEW,
    UI_PERMISSIONS.USER_GROUP_CREATE,
    UI_PERMISSIONS.USER_GROUP_UPDATE,
    UI_PERMISSIONS.USER_GROUP_DELETE,
    API_PERMISSIONS.USER_READ,
    API_PERMISSIONS.USER_WRITE,
    API_PERMISSIONS.USER_DELETE,
    API_PERMISSIONS.USER_GROUP_READ,
    API_PERMISSIONS.USER_GROUP_WRITE,
    API_PERMISSIONS.USER_GROUP_DELETE
  ],

  [USER_GROUPS.SYSTEM_ADMIN]: [
    // System admin can manage system configuration and audit
    UI_PERMISSIONS.SYSTEM_CONFIG_VIEW,
    UI_PERMISSIONS.SYSTEM_CONFIG_UPDATE,
    UI_PERMISSIONS.AUDIT_LOG_VIEW,
    UI_PERMISSIONS.AUDIT_LOG_MANAGE
  ],

  [USER_GROUPS.K8S_ADMIN]: [
    // K8s admin can manage clusters and all k8s resources
    UI_PERMISSIONS.CLUSTER_VIEW,
    UI_PERMISSIONS.CLUSTER_CREATE,
    UI_PERMISSIONS.CLUSTER_UPDATE,
    UI_PERMISSIONS.CLUSTER_DELETE,
    ...Object.values(K8S_PERMISSIONS),
    API_PERMISSIONS.CLUSTER_READ,
    API_PERMISSIONS.CLUSTER_WRITE,
    API_PERMISSIONS.CLUSTER_DELETE
  ],

  [USER_GROUPS.K8S_USER]: [
    // K8s user can view clusters and basic k8s resources
    UI_PERMISSIONS.CLUSTER_VIEW,
    K8S_PERMISSIONS.PODS_LIST,
    K8S_PERMISSIONS.PODS_GET,
    K8S_PERMISSIONS.SERVICES_LIST,
    K8S_PERMISSIONS.SERVICES_GET,
    K8S_PERMISSIONS.DEPLOYMENTS_LIST,
    K8S_PERMISSIONS.DEPLOYMENTS_GET,
    API_PERMISSIONS.CLUSTER_READ
  ],

  [USER_GROUPS.AUDITOR]: [
    // Auditor can view audit logs
    UI_PERMISSIONS.AUDIT_LOG_VIEW,
    API_PERMISSIONS.AUDIT_READ
  ],

  [USER_GROUPS.USER]: [
    // Basic user has minimal permissions
    UI_PERMISSIONS.CLUSTER_VIEW
  ]
}

// Helper functions
export function hasPermission(userPermissions, requiredPermission) {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false
  }

  return userPermissions.some(permission => {
    // 如果权限是字符串格式，进行匹配检查
    if (typeof permission === 'string') {
      // 直接匹配
      if (permission === requiredPermission) {
        return true
      }

      // 全局通配符
      if (permission === '*') {
        return true
      }

      // 检查通配符权限，如 "api:system:*:*" 或 "api:system:*"
      if (permission.includes('*')) {
        const permParts = permission.split(':')
        const reqParts = requiredPermission.split(':')

        // 逐级检查通配符匹配
        for (let i = 0; i < permParts.length; i++) {
          if (permParts[i] === '*') {
            // 如果当前级别是通配符，则匹配成功
            return true
          }
          if (i >= reqParts.length || permParts[i] !== reqParts[i]) {
            // 如果当前级别不匹配，则失败
            return false
          }
        }

        // 如果所有非通配符级别都匹配，则成功
        return true
      }
    }

    // 如果权限是对象格式，构建权限字符串进行比较
    if (permission && permission.resource_type && permission.resource_path && permission.actions) {
      // 构建权限字符串：resource_type:resource_path
      const permissionCode = `${permission.resource_type}:${permission.resource_path}`

      // 检查资源路径是否匹配
      if (permissionCode === requiredPermission) {
        return true
      }

      // 检查是否有通配符权限
      if (permission.resource_path === '*' || permission.actions === '*') {
        const [reqType, reqPath] = requiredPermission.split(':')
        return permission.resource_type === reqType || permission.resource_type === '*'
      }
    }

    return false
  })
}

export function hasAnyPermission(userPermissions, requiredPermissions) {
  return requiredPermissions.some(permission => hasPermission(userPermissions, permission))
}

export function hasAllPermissions(userPermissions, requiredPermissions) {
  return requiredPermissions.every(permission => hasPermission(userPermissions, permission))
}

export function getUserGroupPermissions(groupName) {
  return GROUP_PERMISSIONS[groupName] || []
}

export function checkUserGroupAccess(userGroups, requiredGroups) {
  if (!userGroups || !Array.isArray(userGroups)) {
    return false
  }

  if (!requiredGroups || !Array.isArray(requiredGroups)) {
    return true
  }

  const userGroupNames = userGroups.map(group =>
    typeof group === 'string' ? group : group.name
  )

  return requiredGroups.some(group => userGroupNames.includes(group))
}

export default {
  RESOURCE_TYPES,
  ACTIONS,
  UI_PERMISSIONS,
  API_PERMISSIONS,
  K8S_PERMISSIONS,
  USER_GROUPS,
  GROUP_PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getUserGroupPermissions,
  checkUserGroupAccess
}

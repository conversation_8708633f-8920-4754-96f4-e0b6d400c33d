-- 移除system_configs表中的 oidc_roles_claim 字段
-- 此脚本安全地移除 oidc_roles_claim 字段，因为我们不再使用角色概念

-- 1. 首先检查是否存在 oidc_roles_claim 字段
-- SQLite 不支持直接删除列，需要重建表

-- 2. 创建临时表，不包含 oidc_roles_claim 字段
CREATE TABLE system_configs_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- 基本系统配置
    system_name VARCHAR(100) DEFAULT 'KubeOps',
    system_logo VARCHAR(255),
    system_contact_email VARCHAR(100),
    system_version VARCHAR(20),
    system_debug_mode BOOLEAN DEFAULT FALSE,
    
    -- OIDC配置
    oidc_enabled BOOLEAN DEFAULT FALSE,
    oidc_issuer_url VARCHAR(255),
    oidc_client_id VARCHAR(255),
    oidc_client_secret VARCHAR(255),
    oidc_redirect_uri VARCHAR(255),
    oidc_scopes VARCHAR(255),
    oidc_groups_claim VARCHAR(100),
    
    -- 飞书配置
    feishu_app_id VARCHAR(255),
    feishu_app_secret VARCHAR(255),
    
    -- OBS配置
    obs_enabled BOOLEAN DEFAULT FALSE,
    obs_endpoint VARCHAR(255),
    obs_access_key VARCHAR(255),
    obs_secret_key VARCHAR(255),
    obs_bucket VARCHAR(100),
    obs_region VARCHAR(50),
    obs_encryption_key VARCHAR(255),
    
    -- 审计配置
    audit_retention_days INTEGER DEFAULT 90,
    audit_archive_interval VARCHAR(20),
    audit_archive_enabled BOOLEAN DEFAULT FALSE,
    
    -- Keycloak配置
    keycloak_enabled BOOLEAN DEFAULT FALSE,
    keycloak_server_url VARCHAR(255),
    keycloak_realm VARCHAR(100),
    keycloak_client_id VARCHAR(100),
    keycloak_client_secret VARCHAR(255),
    keycloak_admin_username VARCHAR(100),
    keycloak_admin_password VARCHAR(255),
    
    -- 时间戳
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 3. 复制数据到临时表（排除 oidc_roles_claim 字段）
INSERT INTO system_configs_temp (
    id, system_name, system_logo, system_contact_email, system_version, system_debug_mode,
    oidc_enabled, oidc_issuer_url, oidc_client_id, oidc_client_secret, oidc_redirect_uri, 
    oidc_scopes, oidc_groups_claim,
    feishu_app_id, feishu_app_secret,
    obs_enabled, obs_endpoint, obs_access_key, obs_secret_key, obs_bucket, obs_region, obs_encryption_key,
    audit_retention_days, audit_archive_interval, audit_archive_enabled,
    keycloak_enabled, keycloak_server_url, keycloak_realm, keycloak_client_id, keycloak_client_secret,
    keycloak_admin_username, keycloak_admin_password,
    created_at, updated_at
)
SELECT 
    id, system_name, system_logo, system_contact_email, system_version, system_debug_mode,
    oidc_enabled, oidc_issuer_url, oidc_client_id, oidc_client_secret, oidc_redirect_uri, 
    oidc_scopes, oidc_groups_claim,
    feishu_app_id, feishu_app_secret,
    obs_enabled, obs_endpoint, obs_access_key, obs_secret_key, obs_bucket, obs_region, obs_encryption_key,
    audit_retention_days, audit_archive_interval, audit_archive_enabled,
    keycloak_enabled, keycloak_server_url, keycloak_realm, keycloak_client_id, keycloak_client_secret,
    keycloak_admin_username, keycloak_admin_password,
    created_at, updated_at
FROM system_configs;

-- 4. 删除原表
DROP TABLE system_configs;

-- 5. 重命名临时表
ALTER TABLE system_configs_temp RENAME TO system_configs;

-- 6. 验证迁移结果
-- 检查表结构
PRAGMA table_info(system_configs);

-- 检查数据完整性
SELECT COUNT(*) as total_configs FROM system_configs;

-- 输出完成信息
SELECT 'Migration completed: oidc_roles_claim field removed from system_configs table' as status;

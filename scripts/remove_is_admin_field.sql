-- 移除用户表中的 is_admin 字段
-- 此脚本安全地移除 is_admin 字段，因为我们现在使用 Casbin 进行权限管理

-- 1. 首先检查是否存在 is_admin 字段
-- SQLite 不支持直接删除列，需要重建表

-- 2. 创建临时表，不包含 is_admin 字段
CREATE TABLE users_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100),
    email VARCHAR(100),
    nickname VARCHAR(50),
    avatar VARCHAR(255),
    phone VARCHAR(20),
    status INTEGER DEFAULT 1,
    last_login DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    deleted_at DATETIME,
    oidc_subject VARCHAR(255),
    identity_provider VARCHAR(50),
    feishu_open_id VARCHAR(100),
    feishu_union_id VARCHAR(100),
    feishu_user_id VARCHAR(100)
);

-- 3. 复制数据到临时表（排除 is_admin 字段）
INSERT INTO users_temp (
    id, username, password, email, nickname, avatar, phone, status,
    last_login, created_at, updated_at, deleted_at, oidc_subject,
    identity_provider, feishu_open_id, feishu_union_id, feishu_user_id
)
SELECT 
    id, username, password, email, nickname, avatar, phone, status,
    last_login, created_at, updated_at, deleted_at, oidc_subject,
    identity_provider, feishu_open_id, feishu_union_id, feishu_user_id
FROM users;

-- 4. 删除原表
DROP TABLE users;

-- 5. 重命名临时表
ALTER TABLE users_temp RENAME TO users;

-- 6. 重新创建索引
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE UNIQUE INDEX idx_users_email ON users(email);
CREATE UNIQUE INDEX idx_users_oidc_subject ON users(oidc_subject);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_feishu_open_id ON users(feishu_open_id);
CREATE INDEX idx_users_feishu_union_id ON users(feishu_union_id);
CREATE INDEX idx_users_feishu_user_id ON users(feishu_user_id);

-- 7. 验证迁移结果
-- 检查表结构
PRAGMA table_info(users);

-- 检查数据完整性
SELECT COUNT(*) as total_users FROM users;

-- 输出完成信息
SELECT 'Migration completed: is_admin field removed from users table' as status;

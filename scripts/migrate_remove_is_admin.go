package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 安全地移除用户表中的 is_admin 字段
func main() {
	// 获取数据库路径
	dbPath := "data/kubeops.db"
	if len(os.Args) > 1 {
		dbPath = os.Args[1]
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("开始移除用户表中的 is_admin 字段...")

	// 1. 检查 is_admin 字段是否存在
	var columns []struct {
		Name string `gorm:"column:name"`
	}
	
	err = db.Raw("PRAGMA table_info(users)").Scan(&columns)
	if err != nil {
		log.Fatalf("检查表结构失败: %v", err)
	}

	hasIsAdminField := false
	for _, col := range columns {
		if col.Name == "is_admin" {
			hasIsAdminField = true
			break
		}
	}

	if !hasIsAdminField {
		fmt.Println("✅ is_admin 字段不存在，无需迁移")
		return
	}

	fmt.Println("🔍 发现 is_admin 字段，开始迁移...")

	// 2. 备份当前数据
	var userCount int64
	err = db.Raw("SELECT COUNT(*) FROM users").Scan(&userCount)
	if err != nil {
		log.Fatalf("统计用户数量失败: %v", err)
	}
	fmt.Printf("📊 当前用户数量: %d\n", userCount)

	// 3. 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("开始事务失败: %v", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			log.Fatalf("迁移失败，已回滚: %v", r)
		}
	}()

	// 4. 创建临时表
	fmt.Println("🔄 创建临时表...")
	createTempTableSQL := `
	CREATE TABLE users_temp (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username VARCHAR(50) NOT NULL,
		password VARCHAR(100),
		email VARCHAR(100),
		nickname VARCHAR(50),
		avatar VARCHAR(255),
		phone VARCHAR(20),
		status INTEGER DEFAULT 1,
		last_login DATETIME,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL,
		deleted_at DATETIME,
		oidc_subject VARCHAR(255),
		identity_provider VARCHAR(50),
		feishu_open_id VARCHAR(100),
		feishu_union_id VARCHAR(100),
		feishu_user_id VARCHAR(100)
	)`
	
	err = tx.Exec(createTempTableSQL).Error
	if err != nil {
		tx.Rollback()
		log.Fatalf("创建临时表失败: %v", err)
	}

	// 5. 复制数据
	fmt.Println("📋 复制数据到临时表...")
	copyDataSQL := `
	INSERT INTO users_temp (
		id, username, password, email, nickname, avatar, phone, status,
		last_login, created_at, updated_at, deleted_at, oidc_subject,
		identity_provider, feishu_open_id, feishu_union_id, feishu_user_id
	)
	SELECT 
		id, username, password, email, nickname, avatar, phone, status,
		last_login, created_at, updated_at, deleted_at, oidc_subject,
		identity_provider, feishu_open_id, feishu_union_id, feishu_user_id
	FROM users`
	
	err = tx.Exec(copyDataSQL).Error
	if err != nil {
		tx.Rollback()
		log.Fatalf("复制数据失败: %v", err)
	}

	// 6. 验证数据完整性
	var tempUserCount int64
	err = tx.Raw("SELECT COUNT(*) FROM users_temp").Scan(&tempUserCount)
	if err != nil {
		tx.Rollback()
		log.Fatalf("验证临时表数据失败: %v", err)
	}

	if tempUserCount != userCount {
		tx.Rollback()
		log.Fatalf("数据完整性检查失败: 原表 %d 条记录，临时表 %d 条记录", userCount, tempUserCount)
	}

	fmt.Printf("✅ 数据复制完成，共 %d 条记录\n", tempUserCount)

	// 7. 删除原表
	fmt.Println("🗑️ 删除原表...")
	err = tx.Exec("DROP TABLE users").Error
	if err != nil {
		tx.Rollback()
		log.Fatalf("删除原表失败: %v", err)
	}

	// 8. 重命名临时表
	fmt.Println("🔄 重命名临时表...")
	err = tx.Exec("ALTER TABLE users_temp RENAME TO users").Error
	if err != nil {
		tx.Rollback()
		log.Fatalf("重命名表失败: %v", err)
	}

	// 9. 重新创建索引
	fmt.Println("🔧 重新创建索引...")
	indexes := []string{
		"CREATE UNIQUE INDEX idx_users_username ON users(username)",
		"CREATE UNIQUE INDEX idx_users_email ON users(email)",
		"CREATE UNIQUE INDEX idx_users_oidc_subject ON users(oidc_subject)",
		"CREATE INDEX idx_users_deleted_at ON users(deleted_at)",
		"CREATE INDEX idx_users_feishu_open_id ON users(feishu_open_id)",
		"CREATE INDEX idx_users_feishu_union_id ON users(feishu_union_id)",
		"CREATE INDEX idx_users_feishu_user_id ON users(feishu_user_id)",
	}

	for _, indexSQL := range indexes {
		err = tx.Exec(indexSQL).Error
		if err != nil {
			// 索引创建失败不回滚，只记录警告
			fmt.Printf("⚠️ 创建索引失败: %v\n", err)
		}
	}

	// 10. 提交事务
	err = tx.Commit().Error
	if err != nil {
		log.Fatalf("提交事务失败: %v", err)
	}

	// 11. 最终验证
	var finalUserCount int64
	err = db.Raw("SELECT COUNT(*) FROM users").Scan(&finalUserCount)
	if err != nil {
		log.Fatalf("最终验证失败: %v", err)
	}

	if finalUserCount != userCount {
		log.Fatalf("最终数据完整性检查失败: 原始 %d 条记录，最终 %d 条记录", userCount, finalUserCount)
	}

	fmt.Println("✅ 迁移完成！")
	fmt.Printf("📊 最终用户数量: %d\n", finalUserCount)
	fmt.Println("🎉 is_admin 字段已成功移除")
}
